import argparse
import logging
import os
from logging.handlers import RotatingFileHandler
from src.config.mseclient import <PERSON><PERSON><PERSON><PERSON><PERSON>
from src.llms.llm import load_llm
import uvicorn

# 创建logs目录（如果不存在）
os.makedirs('logs', exist_ok=True)

# 配置日志
log_file_path = 'logs/oneport_ai.log'
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s:%(lineno)d - %(levelname)s - %(message)s",
    handlers=[
        # 输出到控制台
        logging.StreamHandler(),
        # 输出到文件，最大10MB，保留5个备份
        RotatingFileHandler(log_file_path, maxBytes=10*1024*1024, backupCount=5)
    ]
)

logger = logging.getLogger(__name__)
logger.info(f"Logs will be written to: {os.path.abspath(log_file_path)}")

if __name__ == "__main__":
    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Run the OnePort AI API server")
    parser.add_argument(
        "--reload",
        action="store_true",
        help="Enable auto-reload (default: True except on Windows)",
    )
    parser.add_argument(
        "--host",
        type=str,
        default="localhost",
        help="Host to bind the server to (default: localhost)",
    )
    parser.add_argument(
        "--port",
        type=int,
        default=8000,
        help="Port to bind the server to (default: 8000)",
    )
    parser.add_argument(
        "--log-level",
        type=str,
        default="info",
        choices=["debug", "info", "warning", "error", "critical"],
        help="Log level (default: info)",
    )

    parser.add_argument(
        "--config",
        type=str,
        default="online",
        help="Config to load from (default: online)",
    )

    args = parser.parse_args()

    # Determine reload setting
    reload = False

    # Command line arguments override defaults
    if args.reload:
        reload = True

    # load online env config
    if args.config == "online":
        namespace = os.environ.get('namespace', 'test')
        mse_handler = MseHandler()
        mse_handler.load_config_to_env(
            instance_id=os.environ.get('MSE_INSTANCE_ID'),
            data_id=os.environ.get('MSE_DATA_ID', 'oneport_ai'),
            group=os.environ.get('MSE_GROUP', namespace)
        )

        load_llm()
    else:
        load_llm("conf.yaml")
    
    

    logger.info("Starting OnePort_AI API server")
    uvicorn.run(
        "src.server:app",
        host=args.host,
        port=args.port,
        reload=reload,
        log_level=args.log_level,
    )
