# 非流式API版ONEPORT_AI飞书机器人 - 调用/v1/conversation/sync接口
import lark_oapi as lark
from lark_oapi.api.im.v1 import *
import json
import uuid
import asyncio
import sys
import os
import logging
import time
import signal
import threading
import requests

from typing import Dict, Optional
from concurrent.futures import ThreadPoolExecutor
from typing import cast

from src.llms.llm import load_llm
from langgraph.checkpoint.redis.aio import AsyncRedisSaver
from src.config.mseclient import MseHand<PERSON>

from langchain_core.messages import HumanMessage

namespace = os.environ.get('namespace', 'test')
mse_handler = MseHandler()
mse_handler.load_config_to_env(
            instance_id=os.environ.get('MSE_INSTANCE_ID'),
            data_id=os.environ.get('MSE_DATA_ID', 'oneport_ai'),
            group=os.environ.get('MSE_GROUP', namespace)
        )

import src.config.store as store
from src.store.table import ConversationTable, MessageTable
print(f"REDIS_URI: {os.environ['REDIS_URI']}")
REDIS_URI = os.environ['REDIS_URI']
load_llm()
user_id = uuid.uuid4()
thread_id = uuid.uuid4()

user_count = 0


ttl_config = {
    #ttl统一设置为3天
    "default_ttl": 4320,
    "refresh_on_read": True
}



# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建 lark API 客户端用于发送消息
lark_api = lark.Client.builder().app_id("cli_a8d8a5e3d53ad00e").app_secret("iMOoFEvU1JiKYe6TDNj2bcARVKO4pzLV").build()

# 全局变量
executor = ThreadPoolExecutor(max_workers=4)  # 线程池



# 重连相关变量
client = None
is_running = True
reconnect_attempts = 0
max_reconnect_attempts = 10
reconnect_delay = 5  # 初始重连延迟（秒）
max_reconnect_delay = 300  # 最大重连延迟（5分钟）

mcp_settings = {
        "servers": {
            "AmazonSeller": {
                "transport": "stdio",
                "command": "npm",
                "args": ["start", "--prefix", "./amazon_mcp"],
                "enabled_tools": ["getReport", "create_and_get_asin_report", "createAnalyticsReport", "getReportDocument"],
                "add_to_agents": ["chat"],
            }
        }
    }

def get_lark_receive_info(message):
    """统一获取飞书消息的接收ID和类型"""
    if hasattr(message, 'chat_id'):
        return message.chat_id, "chat_id"
    elif hasattr(message, 'sender') and hasattr(message.sender, 'sender_id'):
        return message.sender.sender_id.open_id, "open_id"
    else:
        return message.message_id, "message_id"

async def send_lark_message(lark_api, receive_id_type, receive_id, text):
    content = {"text": text}
    create_req = (
        CreateMessageRequest.builder()
        .receive_id_type(receive_id_type)
        .request_body(
            CreateMessageRequestBody.builder()
            .receive_id(receive_id)
            .content(json.dumps(content))
            .msg_type("text")
            .uuid(str(uuid.uuid4()))
            .build()
        )
        .build()
    )
    create_resp = await lark_api.im.v1.message.acreate(create_req)
    if create_resp.success():
        return create_resp.data.message_id
    return None

async def edit_lark_message(lark_api, message_id, text):
    update_req = (
        UpdateMessageRequest.builder()
        .message_id(message_id)
        .request_body(
            UpdateMessageRequestBody.builder()
            .content(json.dumps({"text": text}))
            .msg_type("text")
            .build()
        )
        .build()
    )
    update_resp = await lark_api.im.v1.message.aupdate(update_req)
    return update_resp.success(), update_resp.msg if not update_resp.success() else None

async def stream_lark_reply(message, user_id, user_message, thread_id, lark_api):
    """流式获取agent输出并实时更新飞书消息"""
    # 不再发送初始消息，直接等待agent输出
    receive_id, receive_id_type = get_lark_receive_info(message)
    message_id = None  # 首次收到内容时再发送
    # 2. 构造oneport_ai输入
    from src.graph.builder import build_chat_graph
    from langchain_core.messages import AIMessageChunk, ToolMessage, BaseMessage
    from uuid import uuid4
    messages = [HumanMessage(content=user_message)] if user_message else []
    chunks = ''
    input_ = {
        "messages": messages,
        "plan_iterations": 0,
        "final_report": "",
        "current_plan": None,
        "observations": [],
    }
    full_content = ""
    last_update_time = time.time()
    edit_count = 0
    MAX_EDIT_COUNT = 15
    sent_length = 0

    chat_graph = build_chat_graph()

    async with AsyncRedisSaver.from_conn_string(redis_url=REDIS_URI, ttl=ttl_config) as memory:
        graph = chat_graph.compile(checkpointer=memory)

        async for agent, _, event_data in graph.astream(
            input_,
            config={
                "thread_id": thread_id,
                "mcp_settings": mcp_settings,
            },
            stream_mode=["messages", "updates"],
            subgraphs=True,
        ):
            logger.info(f"Received event data: {event_data}")
            if isinstance(event_data, tuple) and len(event_data) == 2:
                message_chunk, message_metadata = event_data
            else:
                message_chunk = event_data
                message_metadata = {}
            if isinstance(message_chunk, ToolMessage):
                last_update_time = time.time()
                continue
            if not (isinstance(message_chunk, AIMessageChunk) and message_chunk.content):
                continue
            chunks += message_chunk.content
            full_content += message_chunk.content
            now = time.time()
            if message_id is None:
                # 首次收到内容，直接发送
                message_id = await send_lark_message(lark_api, receive_id_type, receive_id, full_content)
                sent_length = len(full_content)
                last_update_time = now
                edit_count = 1
                continue
            if now - last_update_time >= 0.5:
                # 优先尝试编辑消息
                if edit_count < MAX_EDIT_COUNT:
                    success, err_msg = await edit_lark_message(lark_api, message_id, full_content)
                    if success:
                        last_update_time = now
                        edit_count += 1
                        sent_length = len(full_content)
                    else:
                        if err_msg and "The message has reached the number of times it can be edited." in err_msg:
                            # 发送新消息，只发新增部分
                            new_text = full_content[sent_length:]
                            if new_text:
                                new_id = await send_lark_message(lark_api, receive_id_type, receive_id, new_text)
                                if new_id:
                                    message_id = new_id
                                    edit_count = 0
                                    # 修复：新消息后 full_content 只保留未发过的内容
                                    full_content = new_text
                                    sent_length = len(full_content)
                        else:
                            logger.error(f"更新消息失败: {err_msg}")
                else:
                    # 发送新消息，只发新增部分
                    new_text = full_content[sent_length:]
                    if new_text:
                        new_id = await send_lark_message(lark_api, receive_id_type, receive_id, new_text)
                        if new_id:
                            message_id = new_id
                            edit_count = 0
                            # 修复：新消息后 full_content 只保留未发过的内容
                            full_content = new_text
                            sent_length = len(full_content)


def do_p2_im_message_receive_v1(data: lark.im.v1.P2ImMessageReceiveV1) -> None:
    global user_count, user_id,thread_id
    print(f'[ do_p2_im_message_receive_v1 access ], data: {lark.JSON.marshal(data, indent=4)}')
    message = data.event.message
    message_content = message.content
    if user_count == 10:
        user_count = 0
        user_id = "24acc6ad-7cc7-5c4e-9e84-54b05bc4cff6"
        thread_id = uuid.uuid4()
    else:
        user_count += 1
    try:
        content_dict = json.loads(message_content)
        text_content = content_dict.get("text", "")
        print(f"收到消息内容: {text_content}")
        print(f"用户ID: {user_id}")
        async def process_message():
            try:
                start_time = time.time()
                print(f"开始处理消息: {text_content}")
                await stream_lark_reply(message, user_id, text_content, str(thread_id), lark_api)
                total_time = time.time() - start_time
                print(f"总处理时间: {total_time:.2f}秒")
            except Exception as e:
                print(f"处理消息时出错: {e}")
                import traceback
                traceback.print_exc()
        loop = asyncio.get_event_loop()
        loop.create_task(process_message())
    except json.JSONDecodeError:
        print("消息内容不是有效的JSON格式")
        # 直接用CreateMessageRequest回复格式错误
        content = {"text": "我收到了你的消息，但格式似乎有问题。"}
        if hasattr(message, 'chat_id'):
            receive_id = message.chat_id
            receive_id_type = "chat_id"
        elif hasattr(message, 'sender') and hasattr(message.sender, 'sender_id'):
            receive_id = message.sender.sender_id.open_id
            receive_id_type = "open_id"
        else:
            receive_id = message.message_id
            receive_id_type = "message_id"
        create_req = (
            CreateMessageRequest.builder()
            .receive_id_type(receive_id_type)
            .request_body(
                CreateMessageRequestBody.builder()
                .receive_id(receive_id)
                .content(json.dumps(content))
                .msg_type("text")
                .uuid(str(uuid.uuid4()))
                .build()
            )
            .build()
        )
        asyncio.create_task(lark_api.im.v1.message.acreate(create_req))

# 注册事件 Register event
event_handler = lark.EventDispatcherHandler.builder("", "") \
    .register_p2_im_message_receive_v1(do_p2_im_message_receive_v1) \
    .build()

def create_lark_client():
    """创建飞书客户端"""
    return lark.ws.Client(
        "cli_a8d8a5e3d53ad00e", 
        "iMOoFEvU1JiKYe6TDNj2bcARVKO4pzLV",
        event_handler=event_handler, 
        log_level=lark.LogLevel.DEBUG
    )

def signal_handler(signum, frame):
    """信号处理器"""
    global is_running
    print(f"\n收到信号 {signum}，正在优雅关闭...")
    is_running = False

def run_client_with_reconnect():
    """运行客户端并处理重连"""
    global client, reconnect_attempts, reconnect_delay
    
    while is_running:
        try:
            logger.info(f"正在启动飞书机器人客户端... (尝试 {reconnect_attempts + 1}/{max_reconnect_attempts})")
            
            # 创建新的客户端
            client = create_lark_client()
            
            # 启动客户端
            client.start()
            
            # 如果成功启动，重置重连计数
            reconnect_attempts = 0
            reconnect_delay = 5
            logger.info("飞书机器人客户端启动成功")
            
            # 等待客户端运行
            while is_running:
                time.sleep(1)
                
        except KeyboardInterrupt:
            logger.info("收到中断信号，正在退出...")
            break
        except Exception as e:
            logger.error(f"客户端运行出错: {e}")
            
            if not is_running:
                break
                
            # 重连逻辑
            reconnect_attempts += 1
            if reconnect_attempts >= max_reconnect_attempts:
                logger.error(f"达到最大重连次数 ({max_reconnect_attempts})，停止重连")
                break
            
            # 指数退避重连延迟
            delay = min(reconnect_delay * (2 ** (reconnect_attempts - 1)), max_reconnect_delay)
            logger.info(f"等待 {delay} 秒后重连...")
            time.sleep(delay)

def main():
    global is_running, client
    print("启动ONEPORT_AI飞书机器人（流式版）...")
    print("飞书机器人已启动，等待消息...")

    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    try:
        client_thread = threading.Thread(target=run_client_with_reconnect)
        client_thread.daemon = True
        client_thread.start()
        while is_running:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n正在关闭程序...")
    finally:
        is_running = False
        if client:
            try:
                client.stop()
            except:
                pass
        executor.shutdown(wait=True)
        print("程序已退出")

if __name__ == "__main__":
    main() 