import os
import json
from datetime import datetime, timezone, timedelta

import lark_oapi as lark
from lark_oapi.api.im.v1 import *
from lark_oapi.api.application.v6 import *
from lark_oapi.api.cardkit.v1 import *
from lark_oapi.event.callback.model.p2_card_action_trigger import (
    P2CardActionTrigger,
    P2CardActionTriggerResponse,
)
from lark_oapi.api.cardkit.v1 import *
import uuid
import asyncio
from src.graph.builder import build_chat_graph
from langgraph.checkpoint.redis.aio import AsyncRedisSaver
from src.llms.llm import load_llm
from langchain_core.messages import HumanMessage, AIMessageChunk, ToolMessage
from src.config.mseclient import MseHandler
import threading
import time
import signal
import logging
from dotenv import load_dotenv

logger = logging.getLogger(__name__)
WELCOME_CARD_ID = os.getenv("WELCOME_CARD_ID", "AAqI1lzWtIGUu")

# Load .env file at program startup
load_dotenv()

namespace = os.environ.get('namespace', 'prod')
logger.info(f"namespace: {namespace}")
mse_handler = MseHandler()
mse_handler.load_config_to_env(
            instance_id=os.environ.get('MSE_INSTANCE_ID'),
            data_id=os.environ.get('MSE_DATA_ID', 'oneport_ai'),
            group=os.environ.get('MSE_GROUP', namespace)
        )

import src.config.store as store
from src.store.table import ConversationTable, MessageTable
print(f"REDIS_URI: {os.environ['REDIS_URI']}")
REDIS_URI = os.environ['REDIS_URI']
ttl_config = {
    #ttl统一设置为3天
    "default_ttl": 4320,
    "refresh_on_read": True
}
load_llm()

# 创建 LarkClient 对象，用于请求OpenAPI, 并创建 LarkWSClient 对象，用于使用长连接接收事件。
# Create LarkClient object for requesting OpenAPI, and create LarkWSClient object for receiving events using long connection.
client = lark.Client.builder().app_id("cli_a8eca786017b900b").app_secret("S6bb1B99ZcHBH1UaXyauXKVpkacYEwWc").build()

# 发送消息
# Send a message
# # https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/message/create
def send_message(receive_id_type, receive_id, msg_type, content):
    request = (
        CreateMessageRequest.builder()
        .receive_id_type(receive_id_type)
        .request_body(
            CreateMessageRequestBody.builder()
            .receive_id(receive_id)
            .msg_type(msg_type)
            .content(content)
            .build()
        )
        .build()
    )

    # 使用发送OpenAPI发送通知卡片，你可以在API接口中打开 API 调试台，快速复制调用示例代码
    # Use send OpenAPI to send notice card. You can open the API debugging console in the API interface and quickly copy the sample code for API calls.
    # https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/message/create
    response = client.im.v1.message.create(request)
    if not response.success():
        raise Exception(
            f"client.im.v1.message.create failed, code: {response.code}, msg: {response.msg}, log_id: {response.get_log_id()}"
        )


# 发送欢迎卡片
# Construct a welcome card
# https://open.feishu.cn/document/uAjLw4CM/ukzMukzMukzM/feishu-cards/send-feishu-card#718fe26b
async def send_welcome_card(open_id):
    content = json.dumps(
        {
            "type": "template",
            "data": {
                "template_id": WELCOME_CARD_ID,
                "template_variable": {"open_id": open_id},
            },
        }
    )
    send_message("open_id", open_id, "interactive", content)



def get_stream_chat_card_id(user_query):
    request: CreateCardRequest = CreateCardRequest.builder() \
        .request_body(CreateCardRequestBody.builder()
            .type("card_json")
            .data("{\"schema\":\"2.0\",\"header\":{\"title\":{\"content\":\"小One说：\",\"tag\":\"plain_text\"}},\"config\":{\"streaming_mode\":true,\"summary\":{\"content\":\"\"},\"streaming_config\":{\"print_frequency_ms\":{\"default\":70,\"android\":70,\"ios\":70,\"pc\":50},\"print_step\":{\"default\":1,\"android\":1,\"ios\":1,\"pc\":1},\"print_strategy\":\"fast\"}},\"body\":{\"elements\":[{\"tag\":\"markdown\",\"content\":\"\",\"element_id\":\"markdown_1\"}]}}")
            .build()) \
        .build()

    # 发起请求
    response: CreateCardResponse = client.cardkit.v1.card.create(request)

    # 处理失败返回
    if not response.success():
        lark.logger.error(
            f"client.cardkit.v1.card.create failed, code: {response.code}, msg: {response.msg}, log_id: {response.get_log_id()}, resp: \n{json.dumps(json.loads(response.raw.content), indent=4, ensure_ascii=False)}")
        return
    return response.data.card_id


# 发送流式消息卡片
# Construct an alarm card
# https://open.feishu.cn/document/uAjLw4CM/ukzMukzMukzM/feishu-cards/send-feishu-card#718fe26b
# 替换原send_stream_chat_card为异步流式推送
thread_id = str(uuid.uuid4())
stream_call_count = 0

async def send_stream_chat_card_streaming(card_id, user_query):
    global thread_id, stream_call_count
    stream_call_count += 1
    if stream_call_count % 15 == 0:
        thread_id = str(uuid.uuid4())
    chat_graph = build_chat_graph()
    input_ = {
        "messages": [HumanMessage(content=user_query)],
        "plan_iterations": 0,
        "final_report": "",
        "current_plan": None,
        "observations": [],
    }
    full_content = ""
    element_id = "markdown_1"
    sequence = 1
    mcp_settings = {
        "servers": {
            "AmazonSeller": {
                "transport": "stdio",
                "command": "npm",
                "args": ["start", "--prefix", "./amazon_mcp"],
                "enabled_tools": ["getReport", "create_and_get_asin_report", "createAnalyticsReport", "getReportDocument"],
                "add_to_agents": ["chat"],
            }
        }
    }
    async with AsyncRedisSaver.from_conn_string(redis_url=REDIS_URI, ttl=ttl_config) as memory:
        graph = chat_graph.compile(checkpointer=memory)
        async for agent, _, event_data in graph.astream(
            input_,
            config={
                "thread_id": thread_id,
                "mcp_settings": mcp_settings,
            },
            stream_mode=["messages", "updates"],
            subgraphs=True,
        ):
            if isinstance(event_data, tuple) and len(event_data) == 2:
                message_chunk, _ = event_data
            else:
                message_chunk = event_data
            if isinstance(message_chunk, ToolMessage):
                continue
            if not (isinstance(message_chunk, AIMessageChunk) and message_chunk.content and message_chunk.content.strip()):
                continue
            full_content += message_chunk.content
            send_stream_message(card_id, "markdown_1", full_content, sequence)
            sequence += 1

# 修改原send_stream_chat_card为同步函数，负责卡片创建和启动异步流式推送
async def send_stream_chat_card(receive_id_type, receive_id, user_query):
    card_id = get_stream_chat_card_id(user_query)
    if card_id:
        content = json.dumps(
            {   
                "type": "card",
                "data": {
                    "card_id": card_id,
                },
            }
        )
        send_message(receive_id_type, receive_id, "interactive", content)
        # 启动异步流式推送
        asyncio.create_task(send_stream_chat_card_streaming(card_id, user_query))


def send_stream_message(card_id, element_id, content, sequence):
    # 构造请求对象
    request: ContentCardElementRequest = ContentCardElementRequest.builder() \
        .card_id(card_id) \
        .element_id(element_id) \
        .request_body(ContentCardElementRequestBody.builder()
            .uuid(str(uuid.uuid4()))
            .content(content)
            .sequence(sequence)
            .build()) \
        .build()

    # 发起请求
    response: ContentCardElementResponse = client.cardkit.v1.card_element.content(request)

    # 处理失败返回
    if not response.success():
        lark.logger.error(
            f"client.cardkit.v1.card_element.content failed, code: {response.code}, msg: {response.msg}, log_id: {response.get_log_id()}, resp: \n{json.dumps(json.loads(response.raw.content), indent=4, ensure_ascii=False)}")
        return



# 处理用户进入机器人单聊事件
# handle user enter bot single chat event
# https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/chat-access_event/events/bot_p2p_chat_entered
def do_p2_im_chat_access_event_bot_p2p_chat_entered_v1(data: P2ImChatAccessEventBotP2pChatEnteredV1) -> None:
    open_id = data.event.operator_id.open_id
    asyncio.create_task(send_welcome_card(open_id))

    


# 接收用户发送的消息（包括单聊和群聊），接受到消息后发送欢迎卡片
# Register event handler to handle received messages, including individual chats and group chats.
# https://open.feishu.cn/document/uAjLw4CM/ukTMukTMukTM/reference/im-v1/message/events/receive
def do_p2_im_message_receive_v1(data: P2ImMessageReceiveV1) -> None:
    open_id = data.event.sender.sender_id.open_id
    user_query = data.event.message.content
    if user_query:
        asyncio.create_task(send_stream_chat_card("open_id", open_id, user_query))


# 处理卡片按钮点击回调
# handle card button click callback
# https://open.feishu.cn/document/uAjLw4CM/ukzMukzMukzM/feishu-cards/card-callback-communication
def do_p2_card_action_trigger(data: P2CardActionTrigger) -> P2CardActionTriggerResponse:
    open_id = data.event.operator.open_id
    user_query = data.event.action.input_value
    if data.event.action.value["action"] == "send_stream_chat":
        asyncio.create_task(send_stream_chat_card("open_id", open_id, user_query))
    return P2CardActionTriggerResponse({})


# 注册事件回调
# Register event handler.
event_handler = (
    lark.EventDispatcherHandler.builder("", "")
    .register_p2_im_chat_access_event_bot_p2p_chat_entered_v1(
        do_p2_im_chat_access_event_bot_p2p_chat_entered_v1
    )
    .register_p2_im_message_receive_v1(do_p2_im_message_receive_v1)
    .register_p2_card_action_trigger(do_p2_card_action_trigger)
    .build()
)



wsClient = lark.ws.Client(
    "cli_a8eca786017b900b",
    "S6bb1B99ZcHBH1UaXyauXKVpkacYEwWc",
    event_handler=event_handler,
    log_level=lark.LogLevel.DEBUG,
)


# 只保留简单的wsClient启动线程

def run_ws_client():
    try:
        wsClient.start()
    except Exception as e:
        print(f"[wsClient] ws客户端运行出错: {e}")


def main():
    print("启动ONEPORT_AI飞书卡片机器人（流式版）...")
    print("飞书ws客户端已启动，等待消息...")
    run_ws_client()

if __name__ == "__main__":
    main()
