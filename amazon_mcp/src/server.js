import { McpServer, ResourceTemplate } from '@modelcontextprotocol/sdk/server/mcp.js';
    import { reportsTools } from './tools/reports.js';
    import { apiDocs } from './resources/api-docs.js';

    // Create an MCP server for Amazon SP-API
    const server = new McpServer({
      name: "Amazon Selling Partner API",
      version: "1.0.0",
      description: "MCP Server for interacting with Amazon Selling Partner API"
    });

    // Register all tools
    const registerToolsFromModule = (toolsModule) => {
      Object.entries(toolsModule).forEach(([name, toolConfig]) => {
        server.tool(
          name,
          toolConfig.description,
          toolConfig.schema,
          toolConfig.handler,
        );
      });
    };

    // Register API documentation resources
    server.resource(
      "api_docs",
      new ResourceTemplate("amazon-sp-api://{category}", { list: undefined }),
      async (uri, { category }) => {
        if (!apiDocs[category]) {
          return {
            contents: [{
              uri: uri.href,
              text: `Documentation for category '${category}' not found. Available categories: ${Object.keys(apiDocs).join(', ')}`
            }]
          };
        }
        
        return {
          contents: [{
            uri: uri.href,
            text: apiDocs[category]
          }]
        };
      }
    );

    registerToolsFromModule(reportsTools);

    export { server };
