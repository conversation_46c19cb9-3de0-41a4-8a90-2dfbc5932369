import { z } from 'zod';
import SellingPartnerAPI from 'amazon-sp-api'; // 假设你用的是这个包';
import https from 'https';
import http from 'http';
import zlib from 'zlib';
import { pipeline } from 'stream/promises';

// 初始化SP-API客户端
const sp = new SellingPartnerAPI({
  region: 'na',
  refresh_token: 'Atzr|IwEBIB84b3L3iXpXD9PMlFCECZmt5cvIXGP6bXrRaPzLXwG5r7ga4cCUC9BnJCiXdy1ar1HDBntiO7T9PEzGXW785rcu9-vDoohCqvD4h6d3sQWXEWMwW0G_d-8smWQM9AxlGG4oYXZ8h9mCtV7E9ZQkm5fvC9Ojn_qfVqPYuIdV8cUdiN9f5erhXuXCT9W8K7XmzTKqv_joO9hEJfZzK02qems4Tl7YS66XLucik0uQ7nKlDyDqFTttx3YhGS50IGJBZGwhU0gDnxku4pDxdoQr8M5E08_WZOh8o4KW7FfLWIJpnWiOz4RLLEMSjZKneyLzNS8',
  credentials: {
    SELLING_PARTNER_APP_CLIENT_ID: 'amzn1.application-oa2-client.ee49b566b8384eb6bd7b37b919899dc6',
    SELLING_PARTNER_APP_CLIENT_SECRET: 'amzn1.oa2-cs.v1.2396a921af5e2292fceacdadd617ccf49e1781a55bc2d35bc1aee0181e2a5f83',
    AWS_ACCESS_KEY_ID: '********************',
    AWS_SECRET_ACCESS_KEY: 'WUMcz9QudM0AD86gtpOBab/ddasWm2QRZVA+7XRE',
    AWS_SELLING_PARTNER_ROLE: process.env.SP_API_ROLE_ARN,
  }
});

export const reportsTools = {
  createAnalyticsReport: {
    schema: {
      // reportOptions: z.object({
      //   // asins: z.array(z.string()).optional().describe("A space-separated list of Amazon Standard Identification Numbers (ASINs) for which you can request the report"),
      //   reportPeriod: z.string().optional().describe(`Specifies the reporting period for the report. Values include: 
      //     - DAY
      //     - WEEK
      //     - MONTH
      //     - QUARTER
      //     - YEAR`),
      //   distributorView: z.string().optional().describe(`Specifies the distributor view requested. Values include: 
      //     - MANUFACTURING
      //     - SOURCING`),
      //   sellingProgram: z.string().optional().describe(`Specifies the Amazon selling program for the report. Values include:
      //     - RETAIL
      //     - BUSINESS
      //     - FRESH`),
      // }).describe("Additional information passed to reports. This varies by report type."),
      reportType: z.string().describe(`The report type. Common values include:
- GET_VENDOR_REAL_TIME_INVENTORY_REPORT: Vendor Real Time Inventory Report
- GET_VENDOR_REAL_TIME_SALES_REPORT: Vendor Real Time Sales Report
`),
      marketplaceIds: z.array(z.string()).describe(`A list of marketplace identifiers. Common values include:
        - A2EUQ1WTGCTBG2: Canada
        - ATVPDKIKX0DER: United States of America
        - A1AM78C64UM0Y8: Mexico
        - A2Q3Y263D00KWC: Brazil
        - A28R8C7NBKEWEA: Ireland
        - A1RKKUPIHCS9HS: Spain
        - A1F83G8C2ARO7P: United Kingdom
        - A13V1IB3VIYZZH: France
        - AMEN7PMS3EDWL: Belgium
        - A1805IZSGTT6HS: Netherlands
        - A1PA6795UKMFR9: Germany
        - APJ6JRA9NG5V4: Italy
        - A2NODRKZP88ZB9: Sweden
        - AE08WJ6YKNBMC: South Africa
        - A1C3SOZRARQ6R3: Poland
        - ARBP9OOSHTCHU: Egypt
        - A33AVAJ2PDY3EV: Turkey
        - A17E79C6D8DWNP: Saudi Arabia
        - A2VIGQ35RCS4UG: United Arab Emirates
        - A21TJRUUN4KGV: India
        - A19VAU5U5O7RUS: Singapore
        - A39IBJ37TRP1C6: Australia
        - A1VC38T7YXB528: Japan
      `),
      dataStartTime: z.string().optional().describe("The start of a date and time range, in ISO 8601 format. Defaults to 7 days ago at 00:00:00"),
      dataEndTime: z.string().optional().describe("The end of a date and time range, in ISO 8601 format. Defaults to current date at 00:00:00")
    },
    handler: async ({reportType, marketplaceIds, dataStartTime, dataEndTime }) => {
      try {
        // 计算默认时间
        const now = new Date();
        const endDate = new Date(now.getFullYear(), now.getMonth(), now.getDate()); // 今天00:00:00
        const startDate = new Date(endDate);
        startDate.setDate(startDate.getDate() - 7); // 7天前00:00:00
        
        const defaultEndTime = endDate.toISOString();
        const defaultStartTime = startDate.toISOString();
        
        const params = {
          reportType,
          marketplaceIds: marketplaceIds,
          dataStartTime: dataStartTime || defaultStartTime,
          dataEndTime: dataEndTime || defaultEndTime,
        };
        const data = await sp.callAPI({
          api_path: '/reports/2021-06-30/reports',
          method: 'POST',
          body: params
        });
        return {
          content: [{ type: "text", text: JSON.stringify(data, null, 2) }]
        };
      } catch (error) {
        return {
          content: [{ type: "text", text: `Error creating report: ${error.message}` }],
          isError: true
        };
      }
    },
    description: "Create a analytics report"
  },

  getReport: {
    schema: {
      reportId: z.string().describe("The report ID")
    },
    handler: async ({ reportId }) => {
      try {
        const data = await sp.callAPI({
          api_path: '/reports/2021-06-30/reports/' + reportId,
          method: 'GET'
        });
        return {
          content: [{ type: "text", text: JSON.stringify(data, null, 2) }]
        };
      } catch (error) {
        return {
          content: [{ type: "text", text: `Error retrieving report: ${error.message}` }],
          isError: true
        };
      }
    },
    description: "Returns report details (including the reportDocumentId, if available) for the report that you specify."
  },

  getReportDocument: {
    schema: {
      reportDocumentId: z.string().describe("The report document ID")
    },
    handler: async ({ reportDocumentId }) => {
      try {
        const data = await sp.callAPI({
          api_path: '/reports/2021-06-30/documents/' + reportDocumentId,
          method: 'GET'
        });
        return {
          content: [{ type: "text", text: JSON.stringify(data, null, 2) }]
        };
      } catch (error) {
        return {
          content: [{ type: "text", text: `Error retrieving report document: ${error.message}` }],
          isError: true
        };
      }
    },
    description: "Returns the information required for retrieving a report document's contents."
  },

  getReports: {
    schema: {
      reportTypes: z.array(z.string()).optional().describe(`A list of Analytics report types. Common values include:
- GET_VENDOR_REAL_TIME_INVENTORY_REPORT: Vendor Real Time Inventory Report
- GET_VENDOR_REAL_TIME_TRAFFIC_REPORT: Vendor Real Time Traffic Report
- GET_VENDOR_REAL_TIME_SALES_REPORT: Vendor Real Time Sales Report
- GET_VENDOR_SALES_REPORT: Vendor Sales Report
- GET_VENDOR_NET_PURE_PRODUCT_MARGIN_REPORT: Vendor Net Pure Product Margin Report
- GET_VENDOR_TRAFFIC_REPORT: Vendor Traffic Report
- GET_VENDOR_FORECASTING_REPORT: Vendor Forecasting Report
- GET_VENDOR_INVENTORY_REPORT: Vendor Inventory Report
`),
      processingStatuses: z.array(z.string()).optional().describe(`An array of strings used to filter report processing statuses. Possible values include:
- CANCELLED: The report was cancelled. This can be due to an explicit cancellation request before processing starts, or an automatic cancellation if there is no data to return.
- DONE: The report has completed processing.
- FATAL: The report was aborted due to a fatal error.
- IN_PROGRESS: The report is being processed.
- IN_QUEUE: The report has not yet started processing and may be waiting for another IN_PROGRESS report.
`),
      marketplaceIds: z.array(z.string()).optional().describe(`A list of marketplace identifiers. Common values include:
        - A2EUQ1WTGCTBG2: Canada
        - ATVPDKIKX0DER: United States of America
        - A1AM78C64UM0Y8: Mexico
        - A2Q3Y263D00KWC: Brazil
        - A28R8C7NBKEWEA: Ireland
        - A1RKKUPIHCS9HS: Spain
        - A1F83G8C2ARO7P: United Kingdom
        - A13V1IB3VIYZZH: France
        - AMEN7PMS3EDWL: Belgium
        - A1805IZSGTT6HS: Netherlands
        - A1PA6795UKMFR9: Germany
        - APJ6JRA9NG5V4: Italy
        - A2NODRKZP88ZB9: Sweden
        - AE08WJ6YKNBMC: South Africa
        - A1C3SOZRARQ6R3: Poland
        - ARBP9OOSHTCHU: Egypt
        - A33AVAJ2PDY3EV: Turkey
        - A17E79C6D8DWNP: Saudi Arabia
        - A2VIGQ35RCS4UG: United Arab Emirates
        - A21TJRUUN4KGV: India
        - A19VAU5U5O7RUS: Singapore
        - A39IBJ37TRP1C6: Australia
        - A1VC38T7YXB528: Japan
      `),
      pageSize: z.number().int().optional().describe("The maximum number of reports to return in a single call."),
      createdSince: z.string().optional().describe("The earliest report creation date and time, in ISO 8601 format"),
      createdUntil: z.string().optional().describe("The latest report creation date and time, in ISO 8601 format"),
      nextToken: z.string().optional().describe("A string token returned in the response to your previous request.nextToken is returned when the number of results exceeds the specified pageSize value. To get the next page of results, call the getReports operation and include this token as the only parameter. Specifying nextToken with any other parameters will cause the request to fail."),
    },
    handler: async ({ reportTypes, processingStatuses, marketplaceIds, pageSize, createdSince, createdUntil, nextToken}) => {
      try {
        const params = {
          ...(reportTypes && { reportTypes: reportTypes.join(',') }),
          ...(processingStatuses && { processingStatuses: processingStatuses.join(',') }),
          ...(marketplaceIds && { marketplaceIds: marketplaceIds.join(',') }),
          ...(pageSize && { pageSize }),
          ...(createdSince && { createdSince }),
          ...(createdUntil && { createdUntil }),
          ...(nextToken && { nextToken }),
        };
        const data = await sp.callAPI({
          api_path: '/reports/2021-06-30/reports',
          method: 'GET',
          query: params
        });
        return {
          content: [{ type: "text", text: JSON.stringify(data, null, 2) }]
        };
      } catch (error) {
        return {
          content: [{ type: "text", text: `Error retrieving reports: ${error.message}` }],
          isError: true
        };
      }
    },
    description: "Returns report details for the reports that match the filters that you specify."
  },

  create_and_get_asin_report: {
    schema: {
      reportType: z.string().describe(`The report type. Common values include:
- GET_VENDOR_REAL_TIME_INVENTORY_REPORT: Vendor Real Time Inventory Report
- GET_VENDOR_REAL_TIME_SALES_REPORT: Vendor Real Time Sales Report
`),
      marketplaceIds: z.array(z.string()).describe(`A list of marketplace identifiers. Common values include:
        - A2EUQ1WTGCTBG2: Canada
        - ATVPDKIKX0DER: United States of America
        - A1AM78C64UM0Y8: Mexico
        - A2Q3Y263D00KWC: Brazil
        - A28R8C7NBKEWEA: Ireland
        - A1RKKUPIHCS9HS: Spain
        - A1F83G8C2ARO7P: United Kingdom
        - A13V1IB3VIYZZH: France
        - AMEN7PMS3EDWL: Belgium
        - A1805IZSGTT6HS: Netherlands
        - A1PA6795UKMFR9: Germany
        - APJ6JRA9NG5V4: Italy
        - A2NODRKZP88ZB9: Sweden
        - AE08WJ6YKNBMC: South Africa
        - A1C3SOZRARQ6R3: Poland
        - ARBP9OOSHTCHU: Egypt
        - A33AVAJ2PDY3EV: Turkey
        - A17E79C6D8DWNP: Saudi Arabia
        - A2VIGQ35RCS4UG: United Arab Emirates
        - A21TJRUUN4KGV: India
        - A19VAU5U5O7RUS: Singapore
        - A39IBJ37TRP1C6: Australia
        - A1VC38T7YXB528: Japan
      `),
      dataStartTime: z.string().optional().describe("The start of a date and time range, in ISO 8601 format. Defaults to 7 days ago at 00:00:00"),
      dataEndTime: z.string().optional().describe("The end of a date and time range, in ISO 8601 format. Defaults to current date at 00:00:00"),
      targetAsin: z.string().optional().describe("The specific ASIN to filter data for")
    },
    handler: async ({ reportType, marketplaceIds, dataStartTime, dataEndTime, targetAsin }) => {
      try {
        console.log('=== create_and_get_asin_report 开始执行 ===');
        
        // 计算默认时间
        const now = new Date();
        const endDate = new Date(now.getFullYear(), now.getMonth(), now.getDate()); // 今天00:00:00
        const startDate = new Date(endDate);
        startDate.setDate(startDate.getDate() - 7); // 7天前00:00:00
        
        const defaultEndTime = endDate.toISOString();
        const defaultStartTime = startDate.toISOString();
        
        const finalStartTime = defaultStartTime;
        const finalEndTime = defaultEndTime;
        
        console.log('输入参数:', { reportType, marketplaceIds, dataStartTime: finalStartTime, dataEndTime: finalEndTime, targetAsin });
        
        // 步骤1: 创建报告
        console.log('\n步骤1: 创建报告...');
        const createParams = {
          reportType,
          marketplaceIds: marketplaceIds,
          dataStartTime: finalStartTime,
          dataEndTime: finalEndTime,
        };
        console.log('创建报告参数:', JSON.stringify(createParams, null, 2));
        
        const createResponse = await sp.callAPI({
          api_path: '/reports/2021-06-30/reports',
          method: 'POST',
          body: createParams
        });
        console.log('创建报告响应:', JSON.stringify(createResponse, null, 2));

        if (!createResponse.reportId) {
          throw new Error('Failed to create report: No reportId returned');
        }

        const reportId = createResponse.reportId;
        console.log(`报告创建成功，reportId: ${reportId}`);
        
        let reportStatus = 'IN_QUEUE';
        let reportData = null;
        let attempts = 0;
        const maxAttempts = 60; // 最多等待10分钟 (60 * 10秒)

        // 步骤2: 定时查询报告状态
        console.log('\n步骤2: 监控报告状态...');
        while (reportStatus !== 'DONE' && reportStatus !== 'CANCELLED' && reportStatus !== 'FATAL' && attempts < maxAttempts) {
          console.log(`等待10秒后进行第 ${attempts + 1} 次状态查询...`);
          await new Promise(resolve => setTimeout(resolve, 10000)); // 等待10秒
          attempts++;

          const statusResponse = await sp.callAPI({
            api_path: '/reports/2021-06-30/reports/' + reportId,
            method: 'GET'
          });
          console.log(`第 ${attempts} 次查询结果:`, JSON.stringify(statusResponse, null, 2));

          reportStatus = statusResponse.processingStatus;
          reportData = statusResponse;

          if (reportStatus === 'DONE') {
            console.log('报告处理完成！');
            break;
          } else if (reportStatus === 'CANCELLED' || reportStatus === 'FATAL') {
            throw new Error(`Report processing failed with status: ${reportStatus}`);
          } else {
            console.log(`当前状态: ${reportStatus}, 继续等待...`);
          }
        }

        if (reportStatus !== 'DONE') {
          throw new Error(`Report processing timeout after ${attempts * 10} seconds. Current status: ${reportStatus}`);
        }

        if (!reportData.reportDocumentId) {
          throw new Error('Report completed but no reportDocumentId available');
        }

        // 步骤3: 获取报告文档信息
        console.log('\n步骤3: 获取报告文档信息...');
        console.log('reportDocumentId:', reportData.reportDocumentId);
        
        const documentResponse = await sp.callAPI({
          api_path: '/reports/2021-06-30/documents/' + reportData.reportDocumentId,
          method: 'GET'
        });
        console.log('文档信息响应:', JSON.stringify(documentResponse, null, 2));

        if (!documentResponse.url) {
          throw new Error('No download URL available for report document');
        }

        // 步骤4: 下载并解压报告文件
        console.log('\n步骤4: 下载并解压报告文件...');
        const downloadUrl = documentResponse.url;
        const isCompressed = documentResponse.compressionAlgorithm === 'GZIP';
        console.log('下载URL:', downloadUrl);
        console.log('是否压缩:', isCompressed);
        
        const downloadData = await downloadAndDecompress(downloadUrl, isCompressed);
        console.log('下载的数据长度:', downloadData.length);
        console.log('数据前100字符:', downloadData.substring(0, 100));
        
        // 步骤5: 解析数据并筛选ASIN
        console.log('\n步骤5: 解析数据...');
        let parsedData;
        try {
          // 尝试解析为JSON
          parsedData = JSON.parse(downloadData);
          console.log('数据解析为JSON成功');
        } catch (jsonError) {
          console.log('JSON解析失败，尝试CSV解析:', jsonError.message);
          // 如果不是JSON，尝试解析为CSV
          parsedData = parseCSV(downloadData);
          console.log('CSV解析成功，行数:', Array.isArray(parsedData) ? parsedData.length : 'N/A');
        }

        // 如果指定了targetAsin，筛选相关数据
        let filteredData = parsedData;
        if (targetAsin) {
          console.log(`\n筛选ASIN: ${targetAsin}`);
          filteredData = filterDataByAsin(parsedData, targetAsin);
          console.log('筛选后的数据条数:', Array.isArray(filteredData) ? filteredData.length : 'N/A');
        }

        console.log('\n=== 处理完成 ===');
        
        return {
          content: [{ 
            type: "text", 
            text: JSON.stringify({
              targetAsin: targetAsin,
              reportType: reportType,
              dataCount: Array.isArray(filteredData) ? filteredData.length : 'N/A',
              data: filteredData
            }, null, 2) 
          }]
        };

      } catch (error) {
        console.error('\n=== 错误发生 ===');
        console.error('错误类型:', error.constructor.name);
        console.error('错误消息:', error.message);
        console.error('错误堆栈:', error.stack);
        
        return {
          content: [{ 
            type: "text", 
            text: JSON.stringify({
              success: false,
              error: error.message,
              errorType: error.constructor.name,
              timestamp: new Date().toISOString()
            }, null, 2)
          }],
          isError: true
        };
      }
    },
    description: "Amazon product report tool, you can get the report data of the product, and then you can use the data to analyze the product."
  }
};

// 辅助函数：下载并解压文件
export async function downloadAndDecompress(url, isCompressed) {
  return new Promise((resolve, reject) => {
    const client = url.startsWith('https:') ? https : http;
    
    client.get(url, (response) => {
      if (response.statusCode !== 200) {
        reject(new Error(`Download failed with status: ${response.statusCode}`));
        return;
      }

      let stream = response;
      
      // 如果文件被压缩，创建解压流
      if (isCompressed) {
        stream = response.pipe(zlib.createGunzip());
      }

      let data = '';
      stream.on('data', (chunk) => {
        data += chunk.toString();
      });

      stream.on('end', () => {
        resolve(data);
      });

      stream.on('error', (error) => {
        reject(error);
      });
    }).on('error', (error) => {
      reject(error);
    });
  });
}

// 辅助函数：解析CSV数据
export function parseCSV(csvData) {
  const lines = csvData.trim().split('\n');
  if (lines.length === 0) return [];
  
  const headers = lines[0].split('\t'); // 假设是制表符分隔
  const result = [];
  
  for (let i = 1; i < lines.length; i++) {
    const values = lines[i].split('\t');
    const row = {};
    headers.forEach((header, index) => {
      row[header.trim()] = values[index] ? values[index].trim() : '';
    });
    result.push(row);
  }
  
  return result;
}

// 辅助函数：根据ASIN筛选数据
export function filterDataByAsin(data, targetAsin) {
  console.log('开始筛选ASIN数据，targetAsin:', targetAsin);
  console.log('输入数据类型:', typeof data);
  console.log('输入数据是否为数组:', Array.isArray(data));
  
  // 如果没有目标ASIN，返回原数据
  if (!targetAsin) {
    console.log('没有指定targetAsin，返回原数据');
    return data;
  }
  
  // 检查是否有 reportData 属性
  if (data && typeof data === 'object' && !Array.isArray(data) && data.reportData) {
    console.log('发现 reportData 数组，长度:', data.reportData.length);
    
    // 从 reportData 数组中筛选ASIN相关数据
    const filteredReportData = data.reportData.filter(item => {
      if (typeof item !== 'object') return false;
      
      // 查找可能包含ASIN的字段
      const itemStr = JSON.stringify(item).toLowerCase();
      const targetAsinLower = targetAsin.toLowerCase();
      const isMatch = itemStr.includes(targetAsinLower);
      
      if (isMatch) {
        console.log('找到匹配的数据项:', item);
      }
      
      return isMatch;
    });
    
    console.log('筛选后的 reportData 数量:', filteredReportData.length);
    
    // 直接返回筛选后的数据组成的数组
    return filteredReportData;
  }
  
  // 如果数据本身就是数组
  if (Array.isArray(data)) {
    console.log('输入数据是数组，直接筛选，原始数量:', data.length);
    
    const filtered = data.filter(item => {
      if (typeof item !== 'object') return false;
      
      // 查找可能包含ASIN的字段
      const itemStr = JSON.stringify(item).toLowerCase();
      const targetAsinLower = targetAsin.toLowerCase();
      return itemStr.includes(targetAsinLower);
    });
    
    console.log('筛选后数量:', filtered.length);
    return filtered;
  }
  
  // 如果数据不是数组也没有reportData，尝试在整个对象中查找ASIN
  if (data && typeof data === 'object') {
    console.log('数据是对象但没有reportData，在整个对象中查找');
    const dataStr = JSON.stringify(data).toLowerCase();
    const targetAsinLower = targetAsin.toLowerCase();
    
    if (dataStr.includes(targetAsinLower)) {
      console.log('在对象中找到匹配的ASIN');
      return data;
    } else {
      console.log('在对象中没有找到匹配的ASIN');
      return null;
    }
  }
  
  console.log('数据格式不支持，返回null');
  return null;
}


