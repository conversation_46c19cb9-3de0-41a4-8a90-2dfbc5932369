import { reportsTools } from './src/tools/reports.js';

async function quickTest() {
  console.log('Amazon SP-API Reports 快速测试');
  console.log('================================');

  // 测试参数 - 请根据实际情况修改
  const testParams = {
    reportType: 'GET_VENDOR_REAL_TIME_INVENTORY_REPORT',
    marketplaceIds: ['ATVPDKIKX0DER'], // 美国市场
    dataStartTime: '2025-05-22T00:00:00Z',
    dataEndTime: '2025-05-28T00:00:00Z',
    targetAsin: 'B09Q39ZY44' // 请替换为实际的ASIN
  };

  console.log('测试参数:');
  console.log(JSON.stringify(testParams, null, 2));
  console.log('\n开始测试...\n');

  try {
    const startTime = Date.now();
    
    const result = await reportsTools.create_and_get_asin_report.handler(testParams);
    
    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;
    
    console.log(`\n执行完成，耗时: ${duration}秒`);
    console.log('\n=== 测试结果 ===');
    
    if (result.isError) {
      console.log('❌ 测试失败');
      console.log(result.content[0].text);
    } else {
      console.log('✅ 测试成功');
      
      // 解析结果
      const parsedResult = JSON.parse(result.content[0].text);
      
      console.log(`目标ASIN: ${parsedResult.targetAsin || '无'}`);
      console.log(`数据条数: ${parsedResult.dataCount}`);
      console.log(`数据类型: ${parsedResult.dataType}`);
      
      // 打印完整的数组数据
      if (parsedResult.data) {
        console.log('\n=== 完整数据 ===');
        
        if (Array.isArray(parsedResult.data)) {
          console.log(`✅ 返回的是数组，包含 ${parsedResult.data.length} 个数据项`);
          
          if (parsedResult.data.length > 0) {
            console.log('\n📋 所有数组数据:');
            parsedResult.data.forEach((item, index) => {
              console.log(`\n--- 第 ${index + 1} 项 ---`);
              console.log(JSON.stringify(item, null, 2));
            });
            
            // 如果数据太多，只显示前几项和总结
            if (parsedResult.data.length > 5) {
              console.log(`\n📊 数据摘要: 共 ${parsedResult.data.length} 项数据`);
              console.log('前3项已显示在上方');
            }
          } else {
            console.log('❌ 数组为空，没有找到匹配的ASIN数据');
          }
          
        } else if (parsedResult.data && typeof parsedResult.data === 'object') {
          console.log('📄 返回的是对象:');
          console.log(JSON.stringify(parsedResult.data, null, 2));
          
        } else {
          console.log('⚠️ 数据格式未知:');
          console.log(parsedResult.data);
        }
      } else {
        console.log('❌ 没有返回数据');
      }
      
      // 如果有示例数据，也显示
      if (parsedResult.sampleData && parsedResult.sampleData.length > 0) {
        console.log('\n=== 示例数据 (前3条) ===');
        console.log(JSON.stringify(parsedResult.sampleData, null, 2));
      }
    }
    
  } catch (error) {
    console.log('❌ 测试异常');
    console.error('错误:', error.message);
    console.error('堆栈:', error.stack);
  }
}

// 运行测试
console.log('注意: 这将使用真实的 Amazon SP-API');
console.log('请确保你的凭据正确且有足够的API配额\n');

quickTest().catch(console.error); 