FROM ghcr.io/astral-sh/uv:python3.12-bookworm-slim

# 配置uv使用国内PyPI源
ENV UV_INDEX_URL=https://mirrors.aliyun.com/pypi/simple/
ENV UV_EXTRA_INDEX_URL=https://pypi.org/simple

# Install system dependencies for PostgreSQL and Node.js
RUN apt-get update && apt-get install -y \
    libpq-dev \
    gcc \
    curl \
    supervisor \
    && curl -fsSL https://mirrors.aliyun.com/nodejs-release/setup_lts.x | bash - \
    && apt-get install -y nodejs npm \
    && rm -rf /var/lib/apt/lists/*

# Verify Node.js and npm installation
RUN node --version && npm --version

# Install marp-cli globally
RUN npm install -g @marp-team/marp-cli

# Install uv.
COPY --from=ghcr.io/astral-sh/uv:latest /uv /bin/uv

WORKDIR /app

# Pre-cache the application dependencies.
RUN --mount=type=cache,target=/root/.cache/uv \
    --mount=type=bind,source=uv.lock,target=uv.lock \
    --mount=type=bind,source=pyproject.toml,target=pyproject.toml \
    uv sync --locked --no-install-project

# Copy the application into the container.
COPY . /app

# 确保logs目录存在
RUN mkdir -p /app/logs

# Install Node.js dependencies in amazon_mcp directory
WORKDIR /app/amazon_mcp
RUN npm install

# Return to app directory
WORKDIR /app

# Copy .env.example to .env if .env doesn't exist
RUN if [ -f .env.example ] && [ ! -f .env ]; then cp .env.example .env; fi

# Install the application dependencies.
RUN --mount=type=cache,target=/root/.cache/uv \
    uv sync --locked

EXPOSE 8000

# Run the application.
CMD ["supervisord", "-c", "/app/supervisord.conf"]

