# Search Engine Configuration
SEARCH_API=tavily
TAVILY_API_KEY=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

# Postgres Uri
POSTGRES_URI=postgresql://postgres.fzdqxcrzcpeomlhfajrq:<EMAIL>:5432/postgres

# Redis Uri
REDIS_URI=redis://:<EMAIL>:16379

# Crawl Type
CRAWL_TYPE=firecrawl
CRAWL_API_KEY=fc-aada1c86c1634595891278a2af7b9190

AGENT_RECURSION_LIMIT=50

# Option, for langsmith tracing and monitoring
LANGSMITH_TRACING=true
LANGSMITH_ENDPOINT="https://api.smith.langchain.com"
LANGSMITH_API_KEY="***************************************************"
LANGSMITH_PROJECT="langgraph_test"

# Daytona
DAYTONA_API_KEY=dtn_5cf24f5645f2f5547b5397e57f14e32a98afa4acfe044c7ef00d559c066c8d38
SANDBOX_SNAPSHOT=cxy0317/playwright-vnc-mcp:0.0.6
