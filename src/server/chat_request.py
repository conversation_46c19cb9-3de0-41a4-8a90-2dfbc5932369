from typing import Any, List, Optional, Union

from pydantic import BaseModel, Field
import base64
import httpx
import pandas as pd
import io
from src.store import s3_storage


class ContentItem(BaseModel):
    type: str = Field(..., description="The type of content (text, image, etc.)")
    text: Optional[str] = Field(None, description="The text content if type is 'text'")
    image_url: Optional[str] = Field(
        None, description="The image URL if type is 'image'"
    )


class ChatMessage(BaseModel):
    role: str = Field(
        ..., description="The role of the message sender (user or assistant)"
    )
    content: Union[str, List[ContentItem]] = Field(
        ...,
        description="The content of the message, either a string or a list of content items",
    )


class ChatRequest(BaseModel):
    messages: Optional[List[ChatMessage]] = Field(
        [], description="History of messages between the user and the assistant"
    )
    debug: Optional[bool] = Field(False, description="Whether to enable debug logging")
    conversation_id: Optional[str] = Field(
        None, description="A specific conversation identifier"
    )
    max_plan_iterations: Optional[int] = Field(
        1, description="The maximum number of plan iterations"
    )
    max_step_num: Optional[int] = Field(
        3, description="The maximum number of steps in a plan"
    )
    max_search_results: Optional[int] = Field(
        3, description="The maximum number of search results"
    )
    auto_accepted_plan: Optional[bool] = Field(
        False, description="Whether to automatically accept the plan"
    )
    interrupt_feedback: Optional[str] = Field(
        None, description="Interrupt feedback from the user on the plan"
    )
    mcp_settings: Optional[dict] = Field(
        None, description="MCP settings for the chat request"
    )
    enable_background_investigation: Optional[bool] = Field(
        False, description="Whether to get background investigation before plan"
    )
    type: Optional[str] = Field(
        "chat", description="The type of the request (chat or deep_research)"
    )
    user_id: Optional[str] = Field(
        None, description="The user id of the request"
    )
    resume_from_checkpoint: Optional[bool] = Field(
        False, description="Whether to resume from checkpoint"
    )
    file_paths: Optional[List[str]] = Field(
        [], description="The file paths of the request"
    )
    enable_audio_output: Optional[bool] = Field(
        False, description="Whether to enable audio output"
    )

    def __call__(self):
        if self.file_paths:
            content = [{'type': 'text', 'text': self.messages[0].content}]
            for file_path in self.file_paths:
                if file_path.endswith(".pdf"):
                    data = s3_storage.download_data(file_path)
                    base64_data = base64.b64encode(data).decode("utf-8") 
                    content.append({"type": "file",
                                    "file": {
                                        "filename": file_path.split("/")[-1],
                                        "file_data": "data:application/pdf;base64," + base64_data
                                    }})
                elif file_path.endswith(".xlsx") or file_path.endswith(".xls"):
                    # 下载xlsx文件内容
                    data = s3_storage.download_data(file_path)
                    xlsx_content = data
                    
                    # 将xlsx内容转换为csv
                    try:
                        # 读取xlsx文件到DataFrame
                        df = pd.read_excel(io.BytesIO(xlsx_content))
                        md_string = df.to_markdown(index=False)
                        content.append({"type": "text", "text": md_string})
                    except Exception as e:
                        continue
                elif file_path.endswith(".csv"):
                    data = s3_storage.download_data(file_path)
                    csv_content = data
                    try:
                        df = pd.read_csv(io.BytesIO(csv_content))
                        md_string = df.to_markdown(index=False)
                        content.append({"type": "text", "text": md_string})
                    except Exception as e:
                        continue
                elif file_path.endswith(".txt"):
                    data = s3_storage.download_data(file_path)
                    content.append({"type": "text", "text": data.decode("utf-8")})
                else:
                    continue
            self.messages[0].content = content
        return self


class TTSRequest(BaseModel):
    text: str = Field(..., description="The text to convert to speech")
    voice_type: Optional[str] = Field(
        "BV700_V2_streaming", description="The voice type to use"
    )
    encoding: Optional[str] = Field("mp3", description="The audio encoding format")
    speed_ratio: Optional[float] = Field(1.0, description="Speech speed ratio")
    volume_ratio: Optional[float] = Field(1.0, description="Speech volume ratio")
    pitch_ratio: Optional[float] = Field(1.0, description="Speech pitch ratio")
    text_type: Optional[str] = Field("plain", description="Text type (plain or ssml)")
    with_frontend: Optional[int] = Field(
        1, description="Whether to use frontend processing"
    )
    frontend_type: Optional[str] = Field("unitTson", description="Frontend type")


class GeneratePodcastRequest(BaseModel):
    content: str = Field(..., description="The content of the podcast")


class GeneratePPTRequest(BaseModel):
    content: str = Field(..., description="The content of the ppt")


class GenerateProseRequest(BaseModel):
    prompt: str = Field(..., description="The content of the prose")
    option: str = Field(..., description="The option of the prose writer")
    command: Optional[str] = Field(
        "", description="The user custom command of the prose writer"
    )

class BrowserUseRequest(BaseModel):
    messages: Optional[List[ChatMessage]] = Field(
        [], description="History of messages between the user and the assistant"
    )
    conversation_id: Optional[str] = Field(
        None, description="A specific conversation identifier"
    )
    user_id: Optional[str] = Field(
        None, description="The user id of the request"
    )
