import base64
import json
import logging
import os
import asyncio
from typing import List, cast
from uuid import uuid4
import time
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import Response, StreamingResponse
from langchain_core.messages import <PERSON><PERSON>essageChunk, ToolMessage, BaseMessage
from langgraph.types import Command


# Import store module to ensure it's initialized
import src.store

from src.graph.builder import build_graph_with_memory, build_chat_graph, _build_base_graph
from src.podcast.graph.builder import build_graph as build_podcast_graph
from src.ppt.graph.builder import build_graph as build_ppt_graph
from src.prose.graph.builder import build_graph as build_prose_graph
from src.browser.builder import browser_builder
from src.server.chat_request import (
    ChatMessage,
    ChatRequest,
    GeneratePodcastRequest,
    GeneratePPTRequest,
    GenerateProseRequest,
    TTSRequest,
    BrowserUseRequest
)
from src.server.mcp_request import MCPServerMetadataRequest, MCPServerMetadataResponse
from src.server.mcp_utils import load_mcp_tools
from src.tools import VolcengineTTS
from src.store import ConversationTable, MessageTable
from src.browser.sandbox import create_sandbox, get_or_start_sandbox, wait_sandbox_running

from langgraph.checkpoint.redis.aio import AsyncRedisSaver
from src.config.store import REDIS_URI
from typing import Optional
from src.audio.speech_synthesizer import StreamingSynthesizer

logger = logging.getLogger(__name__)

app = FastAPI(
    title="OnePort AI API",
    description="API for OnePort AI",
    version="0.1.0",
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allows all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allows all methods
    allow_headers=["*"],  # Allows all headers
)

deep_research_builder = _build_base_graph()
chat_builder = build_chat_graph()

ttl_config = {
    #ttl统一设置为3天
    "default_ttl": 4320,
    "refresh_on_read": True
}


@app.post("/v1/conversation")
async def chat_stream(request: ChatRequest):
    # 调用 __call__ 方法处理文件路径
    logger.info(f"Chat request: {request.model_dump()}")
    conversation_id = request.conversation_id
    conversation_type = request.type
    if not conversation_id:
        title = request.messages[0].content
        conversation_id = str(uuid4())
        ConversationTable().create(title=title, user_id=request.user_id, conversation_id=conversation_id, conversation_type=conversation_type, status='running')
    else:
        ConversationTable().update(conversation_id=conversation_id, status='running')
    if not request.interrupt_feedback and not request.resume_from_checkpoint:
        MessageTable().create(
                message_id=str(uuid4()),
                conversation_id=conversation_id,
                content=request.messages[0].content,
                role="user",
                file_paths=request.file_paths,
            )

    request = request()
    request.conversation_id = conversation_id

    logger.info(f"Request: {request.model_dump()}")

    if request.type != "chat":
        return StreamingResponse(
            _astream_workflow_generator(
                request
            ),
            media_type="text/event-stream",
        )
    elif request.type == "chat":
        return StreamingResponse(
            _astream_chat_generator(
                request
            ),
            media_type="text/event-stream",
        )
    else:
        raise HTTPException(status_code=400, detail="Invalid request type")

async def _astream_chat_generator(
    request: ChatRequest
):
    status = 'end'
    last_message_id = str(uuid4())
    messages = request.model_dump()["messages"]
    chunks = ''
    tool_calls = None
    input_ = {
        "messages": messages,
        "plan_iterations": 0,
        "final_report": "",
        "current_plan": None,
        "observations": [],
    }
    speech_synthesizer = None
    message_id = ''
    if request.resume_from_checkpoint:
        input_ = None
    
    try:
        async with AsyncRedisSaver.from_conn_string(redis_url=REDIS_URI, ttl=ttl_config) as memory:
            graph = chat_builder.compile(checkpointer=memory)
            async for agent, _, event_data in graph.astream(
                input_,
                config={
                    "thread_id": request.conversation_id,
                    "mcp_settings": {
                        "servers": {
                            "AmazonSeller": {
                                "transport": "stdio",
                                "command": "npm",
                                "args": ["start", "--prefix", "./amazon_mcp"],
                                "enabled_tools": ["create_and_get_asin_report"],
                                "add_to_agents": ["chat"],
                            }
                        }
                    },
                },
                stream_mode=["messages", "updates"],
                subgraphs=True,
            ):
                if isinstance(event_data, dict) : 
                    continue
                if isinstance(event_data, tuple) and len(event_data) == 2:
                    message_chunk, message_metadata = event_data
                else:
                    message_chunk = event_data
                    message_metadata = {}
                event_stream_message: dict[str, any] = {
                    "conversation_id": request.conversation_id,
                    # "agent": agent[0].split(":")[0],
                    "message_id": message_chunk.id,
                    "role": "assistant",
                    "content": message_chunk.content,
                }
                if message_chunk.response_metadata.get("finish_reason"):
                    event_stream_message["finish_reason"] = message_chunk.response_metadata.get(
                        "finish_reason"
                    )
                    if tool_calls:
                        logger.info(f"Tool calls: {tool_calls}")
                        MessageTable().update(last_message_id, tool_calls=tool_calls)
                        tool_calls = None
                if isinstance(message_chunk, ToolMessage):
                    # Tool Message - Return the result of the tool call
                    event_stream_message["tool_call_id"] = message_chunk.tool_call_id
                    event_stream_message["message_id"] = message_chunk.tool_call_id
                    yield _make_event("tool_call_result", event_stream_message)
                    logger.info(f"Tool call result: {message_chunk.content}")
                    MessageTable().create(message_id=str(uuid4()),
                                          conversation_id=request.conversation_id,
                                          content=message_chunk.content,
                                          parent_message_id=last_message_id,
                                          role="tool",
                                          )
                elif isinstance(message_chunk, AIMessageChunk):
                    if message_chunk.tool_call_chunks:
                        # AI Message - Tool Call Chunks
                        event_stream_message["tool_call_chunks"] = (
                            message_chunk.tool_call_chunks
                        )
                        yield _make_event("tool_call_chunks", event_stream_message)
                        if tool_calls:
                            tool_calls[-1]['args'] += message_chunk.tool_call_chunks[-1]['args']
                        if not tool_calls:
                            tool_calls = message_chunk.tool_call_chunks
                    else:
                        # AI Message - Raw message tokens
                        if not message_id:
                            message_id = str(uuid4())
                        if not speech_synthesizer and request.enable_audio_output:
                            speech_synthesizer = StreamingSynthesizer(message_id)
                        if request.enable_audio_output:
                            speech_synthesizer.streaming_call(message_chunk.content)
                        else:
                            if message_chunk.content:
                                yield _make_event("message_chunk", event_stream_message)
                        chunks += message_chunk.content
                        if message_chunk.content == '' and chunks != '':
                            logger.info(f"Message: {chunks}")
                            file_paths = []
                            if request.enable_audio_output:
                                speech_synthesizer.async_streaming_complete()
                                event_stream_message["content"] = ''
                                while not speech_synthesizer.synthesizer._stopped.is_set():
                                    time.sleep(1)
                                    yield _make_event("message_chunk", event_stream_message)
                                file_paths = [f"audio_file/{message_id}.mp3"]
                                event_stream_message["file_paths"] = file_paths
                                speech_synthesizer = None
                                yield _make_event("message_chunk", event_stream_message)
                            MessageTable().create(
                                message_id=message_id,
                                conversation_id=request.conversation_id,
                                content=chunks,
                                role="assistant",
                                parent_message_id=last_message_id,
                                file_paths=file_paths
                            )
                            chunks = ''
                            message_id = ''
                            last_message_id = message_id
    except asyncio.CancelledError:
        logger.info(f"Chat stream cancelled for thread {request.conversation_id} - client likely disconnected")
        status = 'failed'
        # 发送一个取消事件给客户端（如果连接仍然存在）
        try:
            yield _make_event("cancelled", {
                "conversation_id": request.conversation_id,
                "message": "Stream was cancelled",
                "reason": "client_disconnect"
            })
        except:
            # 如果无法发送取消事件，说明客户端已经断开连接
            pass
        # 重新抛出异常以确保正确的清理
        raise
    except Exception as e:
        logger.error(f"Error in chat stream for thread {request.conversation_id}: {e}")
        status = 'failed'
        # 发送错误事件
        try:
            yield _make_event("error", {
                "conversation_id": request.conversation_id,
                "message": f"An error occurred: {str(e)}",
                "error_type": type(e).__name__
            })
        except:
            pass
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        ConversationTable().update(conversation_id=request.conversation_id, status=status)
        logger.info(f"Conversation {request.conversation_id} status updated to {status}")


async def _astream_workflow_generator(
    request: ChatRequest
):
    status = 'end'
    messages = request.model_dump()["messages"]
    last_message_id = str(uuid4())
    chunks = ''
    tool_calls = None
    input_ = {
        "messages": messages,
        "plan_iterations": 0,
        "final_report": "",
        "current_plan": None,
        "observations": [],
        "auto_accepted_plan": request.auto_accepted_plan,
        "enable_background_investigation": request.enable_background_investigation,
        "research_type": request.type,
    }
    if request.resume_from_checkpoint:
        input_ = None
    elif not request.auto_accepted_plan and request.interrupt_feedback:
        resume_msg = f"[{request.interrupt_feedback}]"
        logger.info(f"Resume message: {resume_msg}")
        # add the last message to the resume message
        if messages:
            resume_msg += f" {messages[-1]['content']}"
        input_ = Command(resume=resume_msg)
    
    enabled_tools = []
    if request.type == "amazon":
        enabled_tools.extend(["createAnalyticsReport", "getReport", "getReportDocument"])
    try:
        async with AsyncRedisSaver.from_conn_string(redis_url=REDIS_URI, ttl= ttl_config) as memory:
            deep_research_graph = deep_research_builder.compile(checkpointer=memory)
            async for agent, _, event_data in deep_research_graph.astream(
                input_,
                config={
                    "thread_id": request.conversation_id,
                    "max_plan_iterations": request.max_plan_iterations,
                    "max_step_num": request.max_step_num,
                    "max_search_results": request.max_search_results,
                    "mcp_settings": {
                        "servers": {
                            "AmazonSeller": {
                                "transport": "stdio",
                                "command": "npm",
                                "args": ["start", "--prefix", "./amazon_mcp"],
                                "enabled_tools": enabled_tools,
                                "add_to_agents": ["amazon"],
                            },

                        }
                    },
                },
                stream_mode=["messages", "updates"],
                subgraphs=True,
            ):
                if isinstance(event_data, dict):
                    if "__interrupt__" in event_data:
                        yield _make_event(
                            "interrupt",
                            {
                                "conversation_id": request.conversation_id,
                                "id": event_data["__interrupt__"][0].ns[0],
                                "role": "interrupt",
                                "content": event_data["__interrupt__"][0].value,
                                "finish_reason": "interrupt",
                                "options": [
                                    {"text": "Edit plan", "value": "edit_plan"},
                                    {"text": "Start research", "value": "accepted"},
                                ],
                            },
                        )
                    continue
                message_chunk, message_metadata = cast(
                    tuple[BaseMessage, dict[str, any]], event_data
                )
                agent_name = get_agent_name(agent)
                role = "assistant"
                if agent_name and "reporter" in agent_name:
                    role = "reporter"
                event_stream_message: dict[str, any] = {
                    "conversation_id": request.conversation_id,
                    "step_type": agent_name,
                    "message_id": message_chunk.id,
                    "role": role,
                    "content": message_chunk.content,
                }
                if message_chunk.response_metadata.get("finish_reason"):
                    event_stream_message["finish_reason"] = message_chunk.response_metadata.get(
                        "finish_reason"
                    )
                    if tool_calls:
                        logger.info(f"Tool calls: {tool_calls}")
                        MessageTable().update(last_message_id, tool_calls=tool_calls)
                        tool_calls = None
                if isinstance(message_chunk, ToolMessage):
                    # Tool Message - Return the result of the tool call
                    tool_call_id = str(uuid4())
                    event_stream_message["tool_call_id"] = tool_call_id
                    event_stream_message["message_id"] = tool_call_id
                    yield _make_event("tool_call_result", event_stream_message)
                    logger.info(f"Tool call result: {message_chunk.content}")
                    MessageTable().create(message_id=tool_call_id,
                                        conversation_id=request.conversation_id,
                                        content=message_chunk.content,
                                        parent_message_id=last_message_id,
                                        role="tool",
                                        )
                elif isinstance(message_chunk, AIMessageChunk):
                    if message_chunk.tool_call_chunks:
                        # AI Message - Tool Call Chunks
                        event_stream_message["tool_call_chunks"] = (
                            message_chunk.tool_call_chunks
                        )
                        yield _make_event("tool_call_chunks", event_stream_message)
                        if tool_calls:
                            if isinstance(tool_calls[-1]['args'], str) and isinstance(message_chunk.tool_call_chunks[-1]['args'], str):
                                tool_calls[-1]['args'] += message_chunk.tool_call_chunks[-1]['args']
                        if not tool_calls:
                            tool_calls = message_chunk.tool_call_chunks
                    else:
                        # AI Message - Raw message tokens
                        if message_chunk.content:
                            yield _make_event("message_chunk", event_stream_message)
                        chunks += message_chunk.content
                        if message_chunk.content == '' and chunks != '':
                            message_id = str(uuid4())
                            logger.info(f"Message: {chunks}")
                            MessageTable().create(
                                message_id=message_id,
                                conversation_id=request.conversation_id,
                                content=chunks,
                                role=role,
                                parent_message_id=last_message_id,
                            )
                            chunks = ''
                            last_message_id = message_id
    except asyncio.CancelledError:
        logger.info(f"Chat stream cancelled for thread {request.conversation_id} - client likely disconnected")
        status = 'failed'
        # 发送一个取消事件给客户端（如果连接仍然存在）
        try:
            yield _make_event("cancelled", {
                "conversation_id": request.conversation_id,
                "message": "Stream was cancelled",
                "reason": "client_disconnect"
            })
        except:
            # 如果无法发送取消事件，说明客户端已经断开连接
            pass
        # 重新抛出异常以确保正确的清理
        raise
    except Exception as e:
        logger.error(f"Error in chat stream for thread {request.conversation_id}: {e}")
        status = 'failed'
        # 发送错误事件
        try:
            yield _make_event("error", {
                "conversation_id": request.conversation_id,
                "message": f"An error occurred: {str(e)}",
                "error_type": type(e).__name__
            })
        except:
            pass
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        ConversationTable().update(conversation_id=request.conversation_id, status=status)
        logger.info(f"Conversation {request.conversation_id} status updated to {status}")



def _make_event(event_type: str, data: dict[str, any]):
    if data.get("content") == "":
        data.pop("content")
    return f"event: {event_type}\ndata: {json.dumps(data, ensure_ascii=False)}\n\n"

def get_agent_name(agent) -> Optional[str]:
    if agent and agent[0] and isinstance(agent[0], str):
        return agent[0].split(":")[0]
    return None


@app.post("/v1/tts")
async def text_to_speech(request: TTSRequest):
    """Convert text to speech using volcengine TTS API."""
    try:
        app_id = os.getenv("VOLCENGINE_TTS_APPID", "")
        if not app_id:
            raise HTTPException(
                status_code=400, detail="VOLCENGINE_TTS_APPID is not set"
            )
        access_token = os.getenv("VOLCENGINE_TTS_ACCESS_TOKEN", "")
        if not access_token:
            raise HTTPException(
                status_code=400, detail="VOLCENGINE_TTS_ACCESS_TOKEN is not set"
            )
        cluster = os.getenv("VOLCENGINE_TTS_CLUSTER", "volcano_tts")
        voice_type = os.getenv("VOLCENGINE_TTS_VOICE_TYPE", "BV700_V2_streaming")

        tts_client = VolcengineTTS(
            appid=app_id,
            access_token=access_token,
            cluster=cluster,
            voice_type=voice_type,
        )
        # Call the TTS API
        result = tts_client.text_to_speech(
            text=request.text[:1024],
            encoding=request.encoding,
            speed_ratio=request.speed_ratio,
            volume_ratio=request.volume_ratio,
            pitch_ratio=request.pitch_ratio,
            text_type=request.text_type,
            with_frontend=request.with_frontend,
            frontend_type=request.frontend_type,
        )

        if not result["success"]:
            raise HTTPException(status_code=500, detail=str(result["error"]))

        # Decode the base64 audio data
        audio_data = base64.b64decode(result["audio_data"])

        # Return the audio file
        return Response(
            content=audio_data,
            media_type=f"audio/{request.encoding}",
            headers={
                "Content-Disposition": (
                    f"attachment; filename=tts_output.{request.encoding}"
                )
            },
        )
    except Exception as e:
        logger.exception(f"Error in TTS endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/v1/podcast/generate")
async def generate_podcast(request: GeneratePodcastRequest):
    try:
        report_content = request.content
        print(report_content)
        workflow = build_podcast_graph()
        final_state = workflow.invoke({"input": report_content})
        audio_bytes = final_state["output"]
        return Response(content=audio_bytes, media_type="audio/mp3")
    except Exception as e:
        logger.exception(f"Error occurred during podcast generation: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/v1/ppt/generate")
async def generate_ppt(request: GeneratePPTRequest):
    try:
        report_content = request.content
        print(report_content)
        workflow = build_ppt_graph()
        final_state = workflow.invoke({"input": report_content})
        generated_file_path = final_state["generated_file_path"]
        with open(generated_file_path, "rb") as f:
            ppt_bytes = f.read()
        return Response(
            content=ppt_bytes,
            media_type="application/vnd.openxmlformats-officedocument.presentationml.presentation",
        )
    except Exception as e:
        logger.exception(f"Error occurred during ppt generation: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/v1/prose/generate")
async def generate_prose(request: GenerateProseRequest):
    try:
        logger.info(f"Generating prose for prompt: {request.prompt}")
        workflow = build_prose_graph()
        
        async def prose_stream_generator():
            try:
                events = workflow.astream(
                    {
                        "content": request.prompt,
                        "option": request.option,
                        "command": request.command,
                    },
                    stream_mode="messages",
                    subgraphs=True,
                )
                async for _, event in events:
                    yield f"data: {event[0].content}\n\n"
            except asyncio.CancelledError:
                logger.info("Prose generation stream cancelled - client likely disconnected")
                # 尝试发送取消事件
                try:
                    yield f"data: [CANCELLED] Prose generation was cancelled\n\n"
                except:
                    pass
                raise
            except Exception as e:
                logger.error(f"Error in prose generation stream: {e}")
                try:
                    yield f"data: [ERROR] {str(e)}\n\n"
                except:
                    pass
                raise
        
        return StreamingResponse(
            prose_stream_generator(),
            media_type="text/event-stream",
        )
    except Exception as e:
        logger.exception(f"Error occurred during prose generation: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/v1/mcp/server/metadata", response_model=MCPServerMetadataResponse)
async def mcp_server_metadata(request: MCPServerMetadataRequest):
    """Get information about an MCP server."""
    try:
        # Set default timeout with a longer value for this endpoint
        timeout = 300  # Default to 300 seconds for this endpoint

        # Use custom timeout from request if provided
        if request.timeout_seconds is not None:
            timeout = request.timeout_seconds

        # Load tools from the MCP server using the utility function
        tools = await load_mcp_tools(
            server_type=request.transport,
            command=request.command,
            args=request.args,
            url=request.url,
            env=request.env,
            timeout_seconds=timeout,
        )

        # Create the response with tools
        response = MCPServerMetadataResponse(
            transport=request.transport,
            command=request.command,
            args=request.args,
            url=request.url,
            env=request.env,
            tools=tools,
        )

        return response
    except Exception as e:
        if not isinstance(e, HTTPException):
            logger.exception(f"Error in MCP server metadata endpoint: {str(e)}")
            raise HTTPException(status_code=500, detail=str(e))
        raise


@app.post("/v1/browser/use")
async def browser_use(request: ChatRequest):
    try:
        title = request.messages[0].content
        # 调用 __call__ 方法处理文件路径
        request = request()
        
        conversation_id = request.conversation_id
        sandbox_id = ''
        if not conversation_id:
            conversation_id = str(uuid4())
            ConversationTable().create(title=title, user_id=request.user_id, conversation_id=conversation_id, sandbox_id=sandbox_id, conversation_type="browser")
        else:
            conversation = ConversationTable().get(conversation_id)
            sandbox_id = conversation.get("sandbox_id")
        if not sandbox_id:
            sandbox = create_sandbox()
            sandbox_id = sandbox.id
            ConversationTable().update(conversation_id, sandbox_id=sandbox_id)
        else:
            sandbox = get_or_start_sandbox(sandbox_id)
        
        if not wait_sandbox_running(sandbox):
            raise HTTPException(status_code=500, detail="Sandbox is not running")
        
        logger.info(f"Sandbox {sandbox_id} is running")
        
        playwright_link = sandbox.get_preview_link(3000)
        vnc_link = sandbox.get_preview_link(6080)
        playwright_url = playwright_link.url if hasattr(playwright_link, 'url') else str(playwright_link).split("url='")[1].split("'")[0]
        playwright_url = playwright_url + "/mcp"
        logger.info(f"Playwright URL: {playwright_url}")
        vnc_url = vnc_link.url if hasattr(vnc_link, 'url') else str(vnc_link).split("url='")[1].split("'")[0]
        logger.info(f"VNC URL: {vnc_url}")
        # playwright_url = "https://3000-8d84a36d-fe73-4375-a603-eaf8029e409f.h1103.daytona.work/mcp"
        # vnc_url = "https://6080-8d84a36d-fe73-4375-a603-eaf8029e409f.h1103.daytona.work"

        
        
        return StreamingResponse(
            browser_stream_generator(request.model_dump()["messages"], conversation_id, playwright_url, vnc_url),
            media_type="text/event-stream",
        )
    except Exception as e:
        logger.exception(f"Error in browser use endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
    
async def browser_stream_generator(messages: List[ChatMessage],
            thread_id: str, playwright_url: str, vnc_url: str):

    input_ = {
    "messages": messages,
    "playwright_url": playwright_url,
    }

    chunks = ''
    tool_calls = None
    last_message_id = str(uuid4())
    MessageTable().create(
        message_id=last_message_id,
        conversation_id=thread_id,
        content=messages[-1]['content'],
        role="user",
    )
    builder = browser_builder()

    event_stream_message: dict[str, any] = {
                "conversation_id": thread_id,
                "vnc_url": vnc_url,
            }

    yield _make_event("message_chunk", event_stream_message)

    async with AsyncRedisSaver.from_conn_string(redis_url=REDIS_URI, ttl=ttl_config) as memory:
        browser_graph = builder.compile(checkpointer=memory)
        events = browser_graph.astream(
            input_,
            config={
                "thread_id": thread_id,
                "recursion_limit": 100
            },
            stream_mode=["messages"],
            subgraphs=True
        )
        async for state, _,event in events:
            message_chunk, message_metadata = cast(
            tuple[BaseMessage, dict[str, any]], event
            )
            event_stream_message: dict[str, any] = {
                "conversation_id": thread_id,
                # "agent": agent[0].split(":")[0],
                "message_id": message_chunk.id,
                "role": "assistant",
                "content": message_chunk.content,
                "vnc_url": vnc_url,
            }
            if message_chunk.response_metadata.get("finish_reason"):
                event_stream_message["finish_reason"] = message_chunk.response_metadata.get(
                    "finish_reason"
                )
                if tool_calls:
                    logger.info(f"Tool calls: {tool_calls}")
                    MessageTable().update(last_message_id, tool_calls=tool_calls)
                    tool_calls = None
            if isinstance(message_chunk, ToolMessage):
                # Tool Message - Return the result of the tool call
                event_stream_message["tool_call_id"] = message_chunk.tool_call_id
                event_stream_message["message_id"] = message_chunk.tool_call_id
                yield _make_event("tool_call_result", event_stream_message)
                logger.info(f"Tool call result: {message_chunk.content}")
                MessageTable().create(message_id=str(uuid4()),
                                    conversation_id=thread_id,
                                    content=message_chunk.content,
                                    parent_message_id=last_message_id,
                                    role="tool",
                                    )
            elif isinstance(message_chunk, AIMessageChunk):
                if message_chunk.tool_call_chunks:
                    # AI Message - Tool Call Chunks
                    event_stream_message["tool_call_chunks"] = (
                        message_chunk.tool_call_chunks
                    )
                    yield _make_event("tool_call_chunks", event_stream_message)
                    if tool_calls:
                        if isinstance(tool_calls[-1]['args'], str) and isinstance(message_chunk.tool_call_chunks[-1]['args'], str):
                            tool_calls[-1]['args'] += message_chunk.tool_call_chunks[-1]['args']
                    if not tool_calls:
                        tool_calls = message_chunk.tool_call_chunks
                else:
                    # AI Message - Raw message tokens
                    yield _make_event("message_chunk", event_stream_message)
                    chunks += message_chunk.content
                    if message_chunk.content == '' and chunks != '':
                        message_id = str(uuid4())
                        logger.info(f"Message: {chunks}")
                        MessageTable().create(
                            message_id=message_id,
                            conversation_id=thread_id,
                            content=chunks,
                            role="assistant",
                            parent_message_id=last_message_id,
                        )
                        chunks = ''
                        last_message_id = message_id
