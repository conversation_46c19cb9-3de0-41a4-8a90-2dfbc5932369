"""
错误处理工具模块

提供统一的异步错误处理功能，特别是针对 LangGraph 的 CancelledError。
"""

import asyncio
import logging
import functools
from typing import AsyncGenerator, Any, Callable, TypeVar, Optional
from contextlib import asynccontextmanager

logger = logging.getLogger(__name__)

T = TypeVar('T')


class StreamCancelledException(Exception):
    """流被取消的自定义异常"""
    def __init__(self, message: str = "Stream was cancelled", reason: str = "unknown"):
        self.message = message
        self.reason = reason
        super().__init__(self.message)


def handle_stream_cancellation(
    thread_id: Optional[str] = None,
    operation_name: str = "operation"
):
    """
    装饰器：处理异步流的取消错误
    
    Args:
        thread_id: 线程ID，用于日志记录
        operation_name: 操作名称，用于日志记录
    """
    def decorator(func: Callable[..., AsyncGenerator[Any, None]]):
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            try:
                async for item in func(*args, **kwargs):
                    yield item
            except asyncio.CancelledError:
                logger.info(
                    f"{operation_name} cancelled for thread {thread_id} - "
                    "client likely disconnected"
                )
                # 尝试发送取消通知
                try:
                    yield _create_cancellation_event(thread_id, operation_name)
                except:
                    # 如果无法发送取消事件，说明客户端已经断开连接
                    pass
                # 重新抛出异常以确保正确的清理
                raise
            except Exception as e:
                logger.error(f"Error in {operation_name} for thread {thread_id}: {e}")
                # 尝试发送错误事件
                try:
                    yield _create_error_event(thread_id, operation_name, e)
                except:
                    pass
                raise
        return wrapper
    return decorator


def _create_cancellation_event(thread_id: Optional[str], operation_name: str) -> dict:
    """创建取消事件"""
    return {
        "event": "cancelled",
        "data": {
            "conversation_id": thread_id,
            "message": f"{operation_name} was cancelled",
            "reason": "client_disconnect"
        }
    }


def _create_error_event(thread_id: Optional[str], operation_name: str, error: Exception) -> dict:
    """创建错误事件"""
    return {
        "event": "error",
        "data": {
            "conversation_id": thread_id,
            "message": f"An error occurred in {operation_name}: {str(error)}",
            "error_type": type(error).__name__
        }
    }


@asynccontextmanager
async def langgraph_stream_context(
    graph,
    input_data: dict,
    config: dict,
    thread_id: Optional[str] = None,
    operation_name: str = "langgraph_stream"
):
    """
    LangGraph 流的上下文管理器，提供统一的错误处理
    
    Args:
        graph: LangGraph 图实例
        input_data: 输入数据
        config: 配置参数
        thread_id: 线程ID
        operation_name: 操作名称
    """
    stream = None
    try:
        logger.info(f"Starting {operation_name} for thread {thread_id}")
        stream = graph.astream(
            input_data,
            config=config,
            stream_mode=["messages", "updates"],
            subgraphs=True,
        )
        yield stream
    except asyncio.CancelledError:
        logger.info(
            f"{operation_name} cancelled for thread {thread_id} - "
            "client likely disconnected"
        )
        raise StreamCancelledException(
            f"{operation_name} was cancelled",
            reason="client_disconnect"
        )
    except Exception as e:
        logger.error(f"Error in {operation_name} for thread {thread_id}: {e}")
        raise
    finally:
        # 清理资源
        if stream:
            try:
                # 如果流还在运行，尝试关闭它
                if hasattr(stream, 'aclose'):
                    await stream.aclose()
            except Exception as e:
                logger.warning(f"Error closing stream: {e}")


async def safe_astream_iteration(
    stream,
    thread_id: Optional[str] = None,
    operation_name: str = "stream_iteration"
):
    """
    安全的异步流迭代器，提供错误处理
    
    Args:
        stream: 异步流
        thread_id: 线程ID
        operation_name: 操作名称
    
    Yields:
        流中的每个项目
    """
    try:
        async for item in stream:
            yield item
    except asyncio.CancelledError:
        logger.info(
            f"{operation_name} iteration cancelled for thread {thread_id} - "
            "client likely disconnected"
        )
        raise StreamCancelledException(
            f"{operation_name} iteration was cancelled",
            reason="client_disconnect"
        )
    except Exception as e:
        logger.error(f"Error in {operation_name} iteration for thread {thread_id}: {e}")
        raise


def create_event_formatter(event_type: str = "message_chunk"):
    """
    创建事件格式化器
    
    Args:
        event_type: 事件类型
    
    Returns:
        格式化函数
    """
    def format_event(data: dict) -> str:
        """格式化事件为 SSE 格式"""
        import json
        if data.get("content") == "":
            data.pop("content", None)
        return f"event: {event_type}\ndata: {json.dumps(data, ensure_ascii=False)}\n\n"
    
    return format_event


class AsyncStreamManager:
    """异步流管理器，提供统一的流处理和错误处理"""
    
    def __init__(self, thread_id: Optional[str] = None, operation_name: str = "stream"):
        self.thread_id = thread_id
        self.operation_name = operation_name
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        
    async def handle_langgraph_stream(
        self,
        graph,
        input_data: dict,
        config: dict,
        message_processor: Optional[Callable] = None
    ) -> AsyncGenerator[Any, None]:
        """
        处理 LangGraph 流，提供统一的错误处理和消息处理
        
        Args:
            graph: LangGraph 图实例
            input_data: 输入数据
            config: 配置参数
            message_processor: 消息处理函数
        
        Yields:
            处理后的消息
        """
        try:
            async with langgraph_stream_context(
                graph, input_data, config, self.thread_id, self.operation_name
            ) as stream:
                async for agent, _, event_data in safe_astream_iteration(
                    stream, self.thread_id, self.operation_name
                ):
                    if message_processor:
                        processed_message = await message_processor(agent, event_data)
                        if processed_message is not None:
                            yield processed_message
                    else:
                        yield (agent, event_data)
                        
        except StreamCancelledException:
            # 流被取消，发送取消事件
            yield _create_cancellation_event(self.thread_id, self.operation_name)
            raise
        except Exception as e:
            # 其他错误，发送错误事件
            yield _create_error_event(self.thread_id, self.operation_name, e)
            raise


# 便捷函数
def make_event(event_type: str, data: dict) -> str:
    """创建 SSE 事件字符串"""
    formatter = create_event_formatter(event_type)
    return formatter(data)


async def with_cancellation_handling(
    async_generator: AsyncGenerator[T, None],
    thread_id: Optional[str] = None,
    operation_name: str = "operation"
) -> AsyncGenerator[T, None]:
    """
    为异步生成器添加取消处理
    
    Args:
        async_generator: 异步生成器
        thread_id: 线程ID
        operation_name: 操作名称
    
    Yields:
        生成器中的每个项目
    """
    try:
        async for item in async_generator:
            yield item
    except asyncio.CancelledError:
        logger.info(
            f"{operation_name} cancelled for thread {thread_id} - "
            "client likely disconnected"
        )
        raise
    except Exception as e:
        logger.error(f"Error in {operation_name} for thread {thread_id}: {e}")
        raise 