import os
import dataclasses
from datetime import datetime, timedelta
from jinja2 import Environment, FileSystemLoader, select_autoescape
from langgraph.prebuilt.chat_agent_executor import AgentState
from src.config.configuration import Configuration
import pytz
# Initialize Jinja2 environment
env = Environment(
    loader=FileSystemLoader(os.path.dirname(__file__)),
    autoescape=select_autoescape(),
    trim_blocks=True,
    lstrip_blocks=True,
)


def get_prompt_template(prompt_name: str) -> str:
    """
    Load and return a prompt template using Jinja2.

    Args:
        prompt_name: Name of the prompt template file (without .md extension)

    Returns:
        The template string with proper variable substitution syntax
    """
    try:
        template = env.get_template(f"{prompt_name}.md")
        return template.render()
    except Exception as e:
        raise ValueError(f"Error loading template {prompt_name}: {e}")

def get_current_time_with_timezone(timezone_str="Asia/Shanghai"):
    """获取指定时区的当前时间，格式化为ISO 8601"""
    tz = pytz.timezone(timezone_str)
    current_time = datetime.now(tz)
    return {
        "timezone": timezone_str,
        "utc_offset": str(current_time.utcoffset()),
        "current_time": current_time.isoformat(),
        "is_dst": current_time.dst() != timedelta(0)
    }


def apply_prompt_template(
    prompt_name: str, state: AgentState, configurable: Configuration = None
) -> list:
    """
    Apply template variables to a prompt template and return formatted messages.

    Args:
        prompt_name: Name of the prompt template to use
        state: Current agent state containing variables to substitute

    Returns:
        List of messages with the system prompt as the first message
    """
    time_info = get_current_time_with_timezone()
    # Convert state to dict for template rendering
    state_vars = {
        "TIME_ZONE": time_info["timezone"],
        "UTC_OFFSET": time_info["utc_offset"],
        "CURRENT_TIME": time_info["current_time"],
        "IS_DST": time_info["is_dst"],
        **state,
    }

    # Add configurable variables
    if configurable:
        state_vars.update(dataclasses.asdict(configurable))

    try:
        template = env.get_template(f"{prompt_name}.md")
        system_prompt = template.render(**state_vars)
        return [{"role": "system", "content": system_prompt}] + state["messages"]
    except Exception as e:
        raise ValueError(f"Error applying template {prompt_name}: {e}")
