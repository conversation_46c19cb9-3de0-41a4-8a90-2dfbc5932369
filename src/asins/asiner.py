import sys
import os
from enum import Enum

from src.asins.jungle_scout import JungleScoutClient
from src.asins.keepa import KeepaClient


class AsinerEngine(Enum):
    KEEPA = "keepa"
    JUNGLE_SCOUT = "jungle_scout"


class Asiner:
    def __init__(self, engine: AsinerEngine = AsinerEngine.JUNGLE_SCOUT):
        """
        初始化产品数据客户端
        
        Args:
            engine: 产品数据客户端类型，可选择 KEEPA 或 JUNGLE_SCOUT
        """
        self.engine = engine
        self._client = None
    
    def _get_client(self):
        """获取产品数据客户端实例"""
        if self._client is None:
            if self.engine == AsinerEngine.JUNGLE_SCOUT:
                self._client = JungleScoutClient()
            elif self.engine == AsinerEngine.KEEPA:
                self._client = KeepaClient()
            else:
                raise ValueError(f"Unsupported asiner engine: {self.engine}")
        return self._client
    
    def get(self, asins: list[str]) -> str:
        """
        根据asins获取产品信息
        
        Args:
            asins: 要获取的asins
            
        Returns:
            str: 产品信息
        """
        
        client = self._get_client()
        products = client.get(asins)
        return products

