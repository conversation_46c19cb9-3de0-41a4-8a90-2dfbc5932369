import os
import json
from dotenv import load_dotenv

import keepa

load_dotenv(override=True)


class KeepaClient:
    def __init__(self):
        self.client = keepa.Keepa(api_key=os.getenv("ASIN_API_KEY"))

    def get(self, asins: list[str]) -> str:
        products = self.client.query(asins)

        asin_price = {}
        for product in products:
            asin_price[product.asin] = product['data']['NEW']
        return json.dumps(asin_price)