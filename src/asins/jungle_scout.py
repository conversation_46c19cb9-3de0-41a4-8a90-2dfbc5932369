import logging
import os
from dotenv import load_dotenv
import json

from junglescout import Client
from junglescout.models.parameters import Marketplace, ApiType, FilterOptions, Sort

logger = logging.getLogger(__name__)
load_dotenv(override=True)


class JungleScoutClient:
    def __init__(self):
        """初始化JungleScout客户端"""
        api_key = os.getenv("ASIN_API_KEY")
        api_key_name = os.getenv("ASIN_API_KEY_NAME")
        if not api_key:
            raise ValueError(
                "JungleScout API key is not set. Please set ASIN_API_KEY environment variable. "
                "Get your API key from https://junglescout.com"
            )
        self.client = Client(
            api_key_name=api_key_name,
            api_key=api_key,
            marketplace=Marketplace.US,
            api_type=ApiType.JS
        )
    
    def get(self, asins: list[str]) -> str:
        """
        根据asins获取产品信息
        
        Args:
            asins: 要获取的asins
            
        Returns:
            str: 产品信息
        """
        try:
            products = self.client.product_database(
                include_keywords=asins,
                marketplace=Marketplace.US,
            )

            asin_price = {}
            for product in products.data:
                asin = product.id.split('/')[-1]
                asin_price[asin] = product.attributes.price

            return json.dumps(asin_price)
                
        except Exception as e:
            logger.error(f"Error get {asins} product info with JungleScout: {str(e)}")
            raise e 