# Firecrawl 爬虫引擎使用指南

## 简介

Firecrawl是一个强大的网页爬取API服务，能够将任何网站转换为干净、结构化的数据。它提供了高质量的Markdown转换和强大的内容提取能力。

## 特性

- **高质量内容提取**: 智能提取网页主要内容
- **多种输出格式**: 支持Markdown、HTML、JSON等格式
- **反爬虫机制**: 内置代理和反反爬虫功能
- **动态内容支持**: 支持JavaScript渲染的页面
- **媒体解析**: 支持PDF、DOCX、图片等文件格式
- **可靠性**: 专为数据获取而设计，无论多困难都能获取数据

## 安装

Firecrawl已包含在项目依赖中，安装项目依赖即可：

```bash
# 使用uv安装依赖
uv sync

# 或使用pip安装
pip install firecrawl-py
```

## 配置

### 获取API密钥

1. 访问 [https://firecrawl.dev](https://firecrawl.dev)
2. 注册账户并获取API密钥
3. 设置环境变量：

```bash
export FIRECRAWL_API_KEY="fc-your-api-key-here"
```

或者在`.env`文件中添加：

```
FIRECRAWL_API_KEY=fc-your-api-key-here
```

## 使用方法

### 基本使用

```python
from src.crawler import Crawler, CrawlerEngine

# 创建Firecrawl爬虫实例
crawler = Crawler(CrawlerEngine.FIRECRAWL)

# 爬取网页
article = crawler.crawl("https://example.com")

print(f"标题: {article.title}")
print(f"内容: {article.to_markdown()}")
```

### 便捷方法

```python
# 使用便捷方法
crawler = Crawler()  # 任何引擎
article = crawler.crawl_with_firecrawl("https://example.com")
```

### 直接使用FirecrawlClient

```python
from src.crawler.firecrawl_client import FirecrawlClient

client = FirecrawlClient()

# 获取Markdown格式内容
markdown_content = client.crawl("https://example.com", return_format="markdown")

# 获取HTML格式内容  
html_content = client.crawl("https://example.com", return_format="html")
```

## Firecrawl vs 其他引擎

| 特性 | Jina | Crawl4ai | Firecrawl |
|------|------|----------|-----------|
| 免费使用 | ✅ (有限制) | ✅ | ❌ (付费服务) |
| 内容质量 | ⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| 反爬虫能力 | ⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| JavaScript支持 | ❌ | ✅ | ✅ |
| 媒体文件支持 | ❌ | ❌ | ✅ |
| API稳定性 | ⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| 速度 | ⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐ |

## 最佳实践

### 1. 错误处理

```python
try:
    crawler = Crawler(CrawlerEngine.FIRECRAWL)
    article = crawler.crawl(url)
    if not article.title and not article.html_content:
        print("爬取结果为空")
except ValueError as e:
    if "API key" in str(e):
        print("请设置FIRECRAWL_API_KEY环境变量")
except Exception as e:
    print(f"爬取失败: {e}")
```

### 2. 选择合适的引擎

- **开发测试**: 使用Jina（免费）
- **生产环境，需要高质量**: 使用Firecrawl
- **本地部署**: 使用Crawl4ai
- **JavaScript重度网站**: 优先选择Firecrawl或Crawl4ai

### 3. 配置管理

建议在项目根目录创建`.env`文件：

```bash
# 爬虫API密钥
JINA_API_KEY=your-jina-api-key
FIRECRAWL_API_KEY=fc-your-firecrawl-api-key

# 其他配置...
```

## 常见问题

### Q: 为什么需要API密钥？
A: Firecrawl是付费服务，需要API密钥来访问。它提供了更高质量的爬取结果和更好的反爬虫能力。

### Q: 如何处理大量爬取请求？
A: Firecrawl支持批量爬取，建议查看官方文档了解批处理API的使用方法。

### Q: 支持哪些网站类型？
A: Firecrawl支持大多数网站类型，包括：
- 静态HTML网站
- JavaScript应用
- 单页应用(SPA)
- 电商网站
- 新闻网站
- 博客和文档站点

### Q: 如何优化爬取性能？
A: 
- 合理设置请求频率
- 使用批量API处理多个URL
- 根据需要选择输出格式（Markdown通常更快）

## 更多资源

- [Firecrawl官方文档](https://docs.firecrawl.dev)
- [Python SDK文档](https://docs.firecrawl.dev/sdks/python)
- [API参考](https://docs.firecrawl.dev/api-reference)

## 示例代码

运行示例代码：

```bash
# 设置API密钥
export FIRECRAWL_API_KEY="fc-your-api-key"

# 运行示例
python src/crawler/example_usage.py
```

这将展示所有三种爬虫引擎的对比和使用方法。 