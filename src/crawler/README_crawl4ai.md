# Crawl4ai 爬虫集成

本模块现在支持两种爬虫引擎：
- **Jina**: 简单易用，有免费额度
- **Crawl4ai**: 功能更强大，内容提取质量更高

## 安装依赖

确保已安装所需依赖：

```bash
# 使用uv安装（推荐）
uv sync

# 或使用pip安装
pip install crawl4ai nest-asyncio
```

## 基本使用

### 1. 使用默认引擎（Jina）

```python
from src.crawler import Crawler

crawler = Crawler()  # 默认使用Jina引擎
article = crawler.crawl("https://example.com")
print(article.title)
print(article.to_markdown())
```

### 2. 指定使用Crawl4ai引擎

```python
from src.crawler import Crawler, CrawlerEngine

# 方法1: 初始化时指定引擎
crawler = Crawler(CrawlerEngine.CRAWL4AI)
article = crawler.crawl("https://example.com")

# 方法2: 使用便捷方法
crawler = Crawler()
article = crawler.crawl_with_crawl4ai("https://example.com")
```

### 3. 比较两种引擎

```python
from src.crawler import Crawler, CrawlerEngine

url = "https://httpbin.org/html"

# Jina引擎
jina_crawler = Crawler(CrawlerEngine.JINA)
jina_article = jina_crawler.crawl(url)

# Crawl4ai引擎
crawl4ai_crawler = Crawler(CrawlerEngine.CRAWL4AI)
crawl4ai_article = crawl4ai_crawler.crawl(url)

print(f"Jina内容长度: {len(jina_article.html_content)}")
print(f"Crawl4ai内容长度: {len(crawl4ai_article.html_content)}")
```

## 高级使用

### 异步爬取（仅Crawl4ai）

```python
import asyncio
from src.crawler import Crawl4aiClient

async def async_crawl():
    client = Crawl4aiClient()
    try:
        # 获取HTML
        html = await client._crawl_async("https://example.com", "html")
        
        # 获取Markdown
        markdown = await client._crawl_async("https://example.com", "markdown")
        
        print(f"HTML长度: {len(html)}")
        print(f"Markdown长度: {len(markdown)}")
    except Exception as e:
        print(f"异步爬取失败: {e}")

# 运行异步函数
asyncio.run(async_crawl())
```

### 直接使用客户端

```python
from src.crawler import JinaClient, Crawl4aiClient

# Jina客户端
jina_client = JinaClient()
html_content = jina_client.crawl("https://example.com", "html")

# Crawl4ai客户端
crawl4ai_client = Crawl4aiClient()
html_content = crawl4ai_client.crawl("https://example.com", "html")
```

## 引擎对比

| 特性 | Jina | Crawl4ai |
|------|------|----------|
| 易用性 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ |
| 内容质量 | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| 速度 | ⭐⭐⭐⭐ | ⭐⭐⭐ |
| 免费额度 | ✅ | ✅ |
| JavaScript支持 | ❌ | ✅ |
| 异步支持 | ❌ | ✅ |
| API密钥需求 | 可选 | 不需要 |

## 配置选项

### Jina配置

设置环境变量以获得更高速率限制：

```bash
export JINA_API_KEY="your_jina_api_key"
```

### Crawl4ai配置

Crawl4ai支持更多配置选项，可以在`Crawl4aiClient`中自定义：

```python
from crawl4ai import BrowserConfig, CrawlerRunConfig

# 自定义浏览器配置
browser_config = BrowserConfig(
    headless=True,
    verbose=True,
    browser_type="chromium"  # 或 "firefox", "webkit"
)

# 自定义爬取配置
run_config = CrawlerRunConfig(
    cache_mode=CacheMode.BYPASS,
    verbose=True
)
```

## 错误处理

```python
from src.crawler import Crawler, CrawlerEngine

crawler = Crawler(CrawlerEngine.CRAWL4AI)

try:
    article = crawler.crawl("https://invalid-url.com")
    if article.html_content:
        print(f"成功: {len(article.html_content)} 字符")
    else:
        print("爬取失败，返回空内容")
except Exception as e:
    print(f"爬取失败: {e}")
```

## 性能建议

1. **对于简单网页**: 使用Jina引擎，速度更快
2. **对于复杂网页**: 使用Crawl4ai引擎，内容提取更准确
3. **对于大量爬取**: 考虑使用Crawl4ai的异步功能
4. **对于JavaScript渲染的页面**: 必须使用Crawl4ai

## 示例代码

查看 `example_usage.py` 文件获取更多使用示例：

```bash
python src/crawler/example_usage.py
```

## 测试

运行测试脚本验证功能：

```bash
python test_crawl4ai.py
```

## 故障排除

### 常见问题

1. **ImportError: No module named 'crawl4ai'**
   ```bash
   pip install crawl4ai
   ```

2. **AsyncIO相关错误**
   ```bash
   pip install nest-asyncio
   ```

3. **浏览器相关错误**
   
   Crawl4ai需要浏览器支持，首次使用时会自动下载：
   ```bash
   # 手动安装浏览器
   python -m playwright install chromium
   ```

### 调试模式

启用详细日志：

```python
import logging
logging.basicConfig(level=logging.DEBUG)

from src.crawler import Crawler, CrawlerEngine
crawler = Crawler(CrawlerEngine.CRAWL4AI)
```

## 实际测试结果

基于我们的测试，两种引擎都能成功爬取内容：

- **Jina引擎**: 适合快速爬取，API简单
- **Crawl4ai引擎**: 功能更强大，支持JavaScript，适合复杂网站

测试URL `https://httpbin.org/html` 的结果：
- 两种引擎都能成功获取 3696 字符的内容
- Crawl4ai还支持Markdown格式输出
- 异步功能正常工作 