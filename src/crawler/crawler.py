import sys
import os
from enum import Enum
from src.crawler.article import Article
from src.crawler.jina_client import JinaClient
from src.crawler.firecrawl_client import FirecrawlClient
from src.crawler.readability_extractor import ReadabilityExtractor


class CrawlerEngine(Enum):
    JINA = "jina"
    FIRECRAWL = "firecrawl"


class Crawler:
    def __init__(self, engine: CrawlerEngine = CrawlerEngine.JINA):
        """
        初始化爬虫
        
        Args:
            engine: 爬虫引擎类型，可选择 JINA 或 FIRECRAWL
        """
        self.engine = engine
        self._client = None
    
    def _get_client(self):
        """获取爬虫客户端实例"""
        if self._client is None:
            if self.engine == CrawlerEngine.JINA:
                self._client = JinaClient()
            elif self.engine == CrawlerEngine.FIRECRAWL:
                self._client = FirecrawlClient()
            else:
                raise ValueError(f"Unsupported crawler engine: {self.engine}")
        return self._client
    
    def crawl(self, url: str) -> Article:
        """
        爬取网页内容并提取文章
        
        Args:
            url: 要爬取的网页URL
            
        Returns:
            Article: 提取的文章对象
        """
        # To help LLMs better understand content, we extract clean
        # articles from HTML, convert them to markdown, and split
        # them into text and image blocks for one single and unified
        # LLM message.
        #
        # Jina and Firecrawl are supported. Jina is simpler and has 
        # a free tier. Firecrawl offers high-quality scraping with 
        # good markdown conversion.
        #
        # Instead of using built-in markdown converters, we use
        # our own solution to get better readability results.
        
        client = self._get_client()
        html = client.crawl(url, return_format="markdown")
        
        extractor = ReadabilityExtractor()
        article = extractor.extract_article(html)
        article.url = url
        return article
    
    def crawl_with_jina(self, url: str) -> Article:
        """使用Jina引擎爬取（向后兼容方法）"""
        original_engine = self.engine
        self.engine = CrawlerEngine.JINA
        self._client = None  # 重置客户端
        try:
            return self.crawl(url)
        finally:
            self.engine = original_engine
            self._client = None
    

    
    def crawl_with_firecrawl(self, url: str) -> Article:
        """使用Firecrawl引擎爬取"""
        original_engine = self.engine
        self.engine = CrawlerEngine.FIRECRAWL
        self._client = None  # 重置客户端
        try:
            return self.crawl(url)
        finally:
            self.engine = original_engine
            self._client = None


if __name__ == "__main__":
    url = "https://www.amazon.com/dp/B0B1DQYWTR?th=1"
    
    # 测试Jina爬虫
    print("=== 使用 Jina 爬虫 ===")
    os.environ["JINA_API_KEY"] = "jina_950a82706c3b474480a06fa834cdeaa2wTQCv-QtqUCJihZlhp2cX-Ht4tjT"
    jina_crawler = Crawler(CrawlerEngine.JINA)
    jina_article = jina_crawler.crawl(url)
    print(f"Jina 标题: {jina_article.title}")
    print(f"Jina 内容: {(jina_article.html_content)}")
    

    
    # 测试Firecrawl爬虫
    print("\n=== 使用 Firecrawl 爬虫 ===")
    # 注意：需要设置 FIRECRAWL_API_KEY 环境变量
    if os.getenv("FIRECRAWL_API_KEY"):
        firecrawl_crawler = Crawler(CrawlerEngine.FIRECRAWL)
        firecrawl_article = firecrawl_crawler.crawl(url)
        print(f"Firecrawl 标题: {firecrawl_article.title}")
        print(f"Firecrawl 内容: {(firecrawl_article.to_markdown())}")
    else:
        print("请设置 FIRECRAWL_API_KEY 环境变量以测试 Firecrawl")
