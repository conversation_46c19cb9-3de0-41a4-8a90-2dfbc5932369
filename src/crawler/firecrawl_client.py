import logging
import os
from dotenv import load_dotenv
from firecrawl import Fire<PERSON>rawlApp

logger = logging.getLogger(__name__)
load_dotenv(override=True)


class FirecrawlClient:
    def __init__(self):
        """初始化Firecrawl客户端"""
        api_key = os.getenv("CRAWL_API_KEY")
        if not api_key:
            raise ValueError(
                "Firecrawl API key is not set. Please set FIRECRAWL_API_KEY environment variable. "
                "Get your API key from https://firecrawl.dev"
            )
        self.app = FirecrawlApp(api_key=api_key)
    
    def crawl(self, url: str, return_format: str = "markdown") -> str:
        """
        爬取网页内容
        
        Args:
            url: 要爬取的网页URL
            return_format: 返回格式，支持 "markdown" 或 "html"
            
        Returns:
            str: 爬取的内容
        """
        try:
            # 根据return_format确定formats参数
            formats = ["markdown"] if return_format == "markdown" else ["html"]
            
            # 使用Firecrawl爬取网页
            result = self.app.scrape_url(url, formats=formats)

            return result.markdown
                
        except Exception as e:
            logger.error(f"Error crawling {url} with Firecrawl: {str(e)}")
            raise e 