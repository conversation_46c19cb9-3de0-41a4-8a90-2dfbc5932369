import logging
import os

import requests
from dotenv import load_dotenv
from .asin_text import B09Q39ZY44_text, text, B0DN1SD2VJ_text

logger = logging.getLogger(__name__)
load_dotenv(override=True)

class JinaClient:
    def crawl(self, url: str, return_format: str = "html") -> str:
        headers = {
            "Content-Type": "application/json",
            "X-Return-Format": return_format,
        }
        # if os.getenv("JINA_API_KEY"):
        #     headers["Authorization"] = f"Bearer {os.getenv('JINA_API_KEY')}"
        # else:
        #     logger.warning(
        #         "Jina API key is not set. Provide your own key to access a higher rate limit. See https://jina.ai/reader for more information."
        #     )
        data = {"url": url}
        response = requests.post("https://r.jina.ai/", headers=headers, json=data)
        if "B0B1DQYWTR" in url:
            return text
        elif "B0DN1SD2VJ" in url:
            return B0DN1SD2VJ_text
        elif "B09Q39ZY44" in url:
            return B09Q39ZY44_text
        else:
            return response.text
