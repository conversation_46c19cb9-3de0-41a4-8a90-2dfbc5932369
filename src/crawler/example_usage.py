#!/usr/bin/env python3
"""
爬虫使用示例

展示如何使用Jina和Firecrawl两种不同的爬虫引擎
"""

import os
import asyncio
from src.crawler import Crawler, CrawlerEngine


def example_basic_usage():
    """基本使用示例"""
    print("=== 基本使用示例 ===")
    
    # 使用默认的Jina引擎
    url = "https://example.com"
    crawler = Crawler()  # 默认使用Jina
    article = crawler.crawl(url)
    print(f"标题: {article.title}")
    print(f"内容长度: {len(article.html_content)}")
    print(f"Markdown预览: {article.to_markdown()[:200]}...")


def example_engine_comparison():
    """比较不同引擎的效果"""
    print("\n=== 引擎比较示例 ===")
    
    url = "https://httpbin.org/html"
    
    # 使用Jina引擎
    print("--- Jina 引擎 ---")
    jina_crawler = Crawler(CrawlerEngine.JINA)
    jina_article = jina_crawler.crawl(url)
    print(f"Jina 标题: {jina_article.title}")
    print(f"Jina 内容长度: {len(jina_article.html_content)}")
    
    # 使用Firecrawl引擎
    print("\n--- Firecrawl 引擎 ---")
    if os.getenv("FIRECRAWL_API_KEY"):
        try:
            firecrawl_crawler = Crawler(CrawlerEngine.FIRECRAWL)
            firecrawl_article = firecrawl_crawler.crawl(url)
            print(f"Firecrawl 标题: {firecrawl_article.title}")
            print(f"Firecrawl 内容长度: {len(firecrawl_article.html_content)}")
        except Exception as e:
            print(f"Firecrawl 测试失败: {e}")
    else:
        print("跳过Firecrawl测试 - 需要设置FIRECRAWL_API_KEY环境变量")


def example_convenience_methods():
    """便捷方法示例"""
    print("\n=== 便捷方法示例 ===")
    
    url = "https://httpbin.org/html"
    crawler = Crawler()
    
    # 使用便捷方法强制使用特定引擎
    print("--- 使用 crawl_with_jina ---")
    jina_article = crawler.crawl_with_jina(url)
    print(f"Jina 结果: {jina_article.title if jina_article.title else '无标题'}")
    
    print("\n--- 使用 crawl_with_firecrawl ---")
    if os.getenv("FIRECRAWL_API_KEY"):
        try:
            firecrawl_article = crawler.crawl_with_firecrawl(url)
            print(f"Firecrawl 结果: {firecrawl_article.title if firecrawl_article.title else '无标题'}")
        except Exception as e:
            print(f"Firecrawl 测试失败: {e}")
    else:
        print("跳过Firecrawl测试 - 需要设置FIRECRAWL_API_KEY环境变量")


async def example_async_usage():
    """异步使用示例（演示异步概念）"""
    print("\n=== 异步使用示例 ===")
    
    url = "https://httpbin.org/html"
    
    try:
        # 使用标准爬虫进行异步模拟
        import asyncio
        
        async def async_crawl():
            crawler = Crawler(CrawlerEngine.JINA)
            return crawler.crawl(url)
        
        result = await async_crawl()
        print(f"异步爬取完成: {result.title if result.title else '无标题'}")
        print(f"内容长度: {len(result.html_content)}")
        
    except Exception as e:
        print(f"异步测试失败: {e}")


def example_firecrawl_features():
    """Firecrawl特殊功能示例"""
    print("\n=== Firecrawl特殊功能示例 ===")
    
    if not os.getenv("FIRECRAWL_API_KEY"):
        print("跳过Firecrawl特殊功能测试 - 需要设置FIRECRAWL_API_KEY环境变量")
        return
    
    try:
        from src.crawler.firecrawl_client import FirecrawlClient
        client = FirecrawlClient()
        
        # 测试不同网站类型
        test_urls = [
            "https://example.com",
            "https://httpbin.org/html",
        ]
        
        for url in test_urls:
            print(f"\n--- 测试URL: {url} ---")
            try:
                # 获取markdown格式
                markdown_content = client.crawl(url, "markdown")
                print(f"Markdown内容长度: {len(markdown_content)}")
                print(f"Markdown预览: {markdown_content[:150]}...")
                
                # 获取HTML格式
                html_content = client.crawl(url, "html")
                print(f"HTML内容长度: {len(html_content)}")
                
            except Exception as e:
                print(f"测试{url}失败: {e}")
                
    except Exception as e:
        print(f"Firecrawl客户端初始化失败: {e}")


def example_error_handling():
    """错误处理示例"""
    print("\n=== 错误处理示例 ===")
    
    # 测试无效URL
    invalid_url = "https://this-domain-does-not-exist-12345.com"
    crawler = Crawler(CrawlerEngine.JINA)
    
    try:
        article = crawler.crawl(invalid_url)
        if article.title or article.html_content:
            print(f"意外成功: {article.title}")
        else:
            print("爬取失败，返回空内容")
    except Exception as e:
        print(f"预期的错误: {type(e).__name__}: {str(e)}")


if __name__ == "__main__":
    # 设置API密钥提示
    if not os.getenv("JINA_API_KEY"):
        print("提示: 设置JINA_API_KEY环境变量可以获得更高的速率限制")
    
    if not os.getenv("FIRECRAWL_API_KEY"):
        print("提示: 设置FIRECRAWL_API_KEY环境变量以测试Firecrawl功能")
        print("      获取API密钥: https://firecrawl.dev")
    
    # 运行示例
    example_basic_usage()
    example_engine_comparison()
    example_convenience_methods()
    
    # 运行异步示例
    print("\n运行异步示例...")
    asyncio.run(example_async_usage())
    
    # 运行Firecrawl特殊功能示例
    example_firecrawl_features()
    
    example_error_handling()
    
    print("\n=== 所有示例完成 ===") 