from daytona_sdk import Daytona, DaytonaConfig, CreateSandboxFromSnapshotParams, Sandbox, SessionExecuteRequest, SandboxState
from src.config.sandbox import DAYTONA_API_KEY, SANDBOX_SNAPSHOT
import logging
import time

logger = logging.getLogger(__name__)

config = DaytonaConfig(api_key=DAYTONA_API_KEY)
daytona = Daytona(config)

def create_sandbox():
    params = CreateSandboxFromSnapshotParams(snapshot=SANDBOX_SNAPSHOT, public=True, auto_stop_interval=30, auto_archive_interval=24 * 60)
    sandbox = daytona.create(params)
    logger.info(f"Sandbox created: {sandbox.id}")
    return sandbox

def get_or_start_sandbox(sandbox_id: str):
    sandbox = daytona.get(sandbox_id)
    if sandbox.state == SandboxState.STOPPED or sandbox.state == SandboxState.ARCHIVED:
        logger.info(f"Sandbox {sandbox_id} is in {sandbox.state}, starting it...")
        try:
            sandbox.start()
            logger.info(f"Sandbox {sandbox_id} started")
        except Exception as e:
            logger.error(f"Error starting sandbox {sandbox_id}: {e}")
            raise e
    logger.info(f"Sandbox {sandbox_id} is in {sandbox.state}, using it...")
    return sandbox

def wait_sandbox_running(sandbox: Sandbox) -> bool:
    while True:
        max_retries = 3
        if sandbox.state == SandboxState.STARTED:
            return True
        time.sleep(5)
        max_retries -= 1
        if max_retries <= 0:
            logger.error(f"Sandbox {sandbox.id} is in {sandbox.state}, timeout")
            return False
        
    

def start_supervisord_session(sandbox: Sandbox):
    """Start supervisord in a session."""
    session_id = "supervisord-session"
    try:
        logger.info(f"Creating session {session_id} for supervisord")
        sandbox.process.create_session(session_id)
        
        # Execute supervisord command
        sandbox.process.execute_session_command(session_id, SessionExecuteRequest(
            command="exec /usr/bin/supervisord -n -c /etc/supervisor/conf.d/supervisord.conf",
            run_async=True
        ))
        logger.info(f"Supervisord started in session {session_id}")
    except Exception as e:
        logger.error(f"Error starting supervisord session: {str(e)}")
        raise e

def get_playwright_vnc_url() -> tuple[str, str, str]:
    sandbox = create_sandbox()

    playwright_link = sandbox.get_preview_link(3000)
    vnc_link = sandbox.get_preview_link(6080)

    playwright_url = playwright_link.url if hasattr(playwright_link, 'url') else str(playwright_link).split("url='")[1].split("'")[0]
    vnc_url = vnc_link.url if hasattr(vnc_link, 'url') else str(vnc_link).split("url='")[1].split("'")[0]

    playwright_url = playwright_url + "/mcp"
    logger.info(f"Playwright URL: {playwright_url}")
    vnc_url = vnc_url + "/vnc.html"
    logger.info(f"VNC URL: {vnc_url}")

    return sandbox.id, playwright_url, vnc_url

def stop_sandbox(sandbox_id: str):
    try:
        daytona.stop(sandbox_id)
        logger.info(f"Sandbox stopped: {sandbox_id}")
    except Exception as e:
        logger.error(f"Error stopping sandbox: {e}")
        raise e

def delete_sandbox(sandbox_id: str):
    try:
        daytona.delete(sandbox_id)
        logger.info(f"Sandbox deleted: {sandbox_id}")
    except Exception as e:
        logger.error(f"Error deleting sandbox: {e}")
        raise e




if __name__ == "__main__":
    playwright_url, vnc_url = get_playwright_vnc_url()
    print(playwright_url)
    print(vnc_url)