from typing import Optional, List, Dict, Any

from langgraph.graph import MessagesState


class BrowserState(MessagesState):
    """State for the browser use functionality."""

    # Input
    input: str = ""
    task: str = ""
    
    # Browser environment
    playwright_url: Optional[str] = None
    
    # Browser actions and results
    browser_actions: List[Dict[str, Any]] = []
    screenshots: List[str] = []  # Base64 encoded screenshots
    
    # Output
    browser_response: str = ""
    success: bool = False
    error_message: Optional[str] = None
    
    # Cleanup tracking
    cleanup_completed: bool = False 