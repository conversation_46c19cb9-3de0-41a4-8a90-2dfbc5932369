from langgraph.graph import END, START, StateGraph

from src.browser.browser_use_node import browser_use_node
from src.browser.state import B<PERSON>erState


def browser_builder():
    """Build and return the browser workflow graph."""
    # Build state graph
    builder = StateGraph(BrowserState)
    
    # Add browser use node
    builder.add_node("browser_use", browser_use_node)
    
    # Define the flow
    builder.add_edge(START, "browser_use")
    builder.add_edge("browser_use", END)
    
    return builder


if __name__ == "__main__":
    from dotenv import load_dotenv
    import asyncio
    import logging
    
    # Configure logging
    logging.basicConfig(level=logging.INFO)
    
    # Load environment variables
    load_dotenv(override=True)
    
    async def test_browser_workflow():
        """Test the browser workflow"""
        try:
            # Create initial state
            initial_state = {
                "messages": [],
                "input": "请访问百度首页并截取屏幕截图",
                "task": "访问百度首页并截取屏幕截图"
            }
            
            # Run the workflow
            final_state = await workflow.ainvoke(initial_state)
            
            print("Browser workflow completed!")
            print(f"Success: {final_state.get('success', False)}")
            print(f"Response: {final_state.get('browser_response', 'No response')[:200]}...")
            print(f"Sandbox ID: {final_state.get('sandbox_id')}")
            print(f"Playwright URL: {final_state.get('playwright_url')}")
            print(f"VNC URL: {final_state.get('vnc_url')}")
            
        except Exception as e:
            print(f"Error running browser workflow: {e}")
    
    # Run the test
    asyncio.run(test_browser_workflow()) 