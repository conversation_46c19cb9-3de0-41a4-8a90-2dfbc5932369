import asyncio
import json
import logging
from typing import Literal
import time
from langchain_core.messages import HumanMessage, AIMessage
from langchain_core.runnables import RunnableConfig
from langgraph.types import Command

from src.browser.state import BrowserState
from src.agents.agents import create_agent

from langchain_mcp_adapters.client import MultiServerMCPClient

from src.tools import (
    get_web_search_tool,
)

logger = logging.getLogger(__name__)


async def browser_use_node(
    state: BrowserState, config: RunnableConfig
) -> Command[Literal["__end__"]]:
    """
    Browser use node that creates sandbox, sets up MCP, and performs Q&A with LLM.
    
    Flow:
    1. Create or reuse sandbox and get playwright URL
    2. Set up MCP client with connection manager for long-lived connections
    3. Load available tools from MCP server (with caching)
    4. Use create_agent to create browser agent with tools
    5. Execute the task using the agent
    """
    logger.info("Browser use node is running.")
    
    playwright_url = state.get("playwright_url")

    try:
        enable_tools = []
        mcp_servers = {
            "playwright": {
                "transport": "streamable_http", 
                "url": playwright_url,
            }
        }
        while True:
            max_retries = 3
            try:
                client = MultiServerMCPClient(mcp_servers)
                tools = await client.get_tools()
                for tool in tools:
                    if tool.name != "browser_take_screenshot":
                        enable_tools.append(tool)
                break
            except Exception as e:
                max_retries -= 1
                if max_retries <= 0:
                    logger.error(f"Error in browser use node: {e}")
                    raise e
                time.sleep(10)

        enable_tools.append(get_web_search_tool(3))
        
        # Step 2: Create browser agent using create_agent
        logger.info("Creating browser agent...")
        browser_agent = create_agent(
            agent_name="browser",
            agent_type="browser", 
            tools=enable_tools,
            prompt_template="browser"
        )
        
        # Step 5: Prepare state for agent
        messages = state.get("messages", [])
        
        # Step 6: Execute the agent
        logger.info("Executing browser agent...")
        agent_input = {"messages": messages}

        result = await browser_agent.ainvoke(agent_input, config)
        
        # Step 7: Extract response from result
        if result.get("messages"):
            last_message = result["messages"][-1]
            browser_response = last_message.content if hasattr(last_message, 'content') else str(last_message)
        else:
            browser_response = "Browser task completed successfully."
        
        logger.info(f"Browser use completed. Response length: {len(browser_response)}")
        
        
        return Command(
            update={
                "success": True,
                "messages": result.get("messages", []),
            },
            goto="__end__",
        )
        
    except Exception as e:
        logger.error(f"Error in browser use node: {e}")
        error_message = f"Browser use failed: {str(e)}"
        
        return Command(
            update={
                "error_message": error_message,
                "success": False,
                "messages": (state.get("messages") or []) + [AIMessage(content=error_message)]
            },
            goto="__end__",
        )

