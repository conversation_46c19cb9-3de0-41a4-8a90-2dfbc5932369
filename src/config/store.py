import os
import enum
import logging
from dotenv import load_dotenv

logger = logging.getLogger(__name__)

load_dotenv(override=True)

# postgres configuration
POSTGRES_URI = os.getenv("POSTGRES_URI")

# redis configuration
REDIS_URI = os.getenv("REDIS_URI")

# storage configuration
S3_AK = os.getenv("S3_AK")
S3_SK= os.getenv("S3_SK")
S3_ENDPOINT = os.getenv("S3_ENDPOINT")
S3_REGION = os.getenv("S3_REGION")
S3_BUCKET_NAME = os.getenv("S3_BUCKET_NAME")

# Validate POSTGRES_URI
if not POSTGRES_URI:
    logger.warning("POSTGRES_URI environment variable is not set. Database functionality will be disabled.")
else:
    # Log the first part of the URI for debugging (hide credentials)
    logger.info(f"POSTGRES_URI get successfully.")

if not REDIS_URI:
    logger.warning("REDIS_URI environment variable is not set. Redis functionality will be disabled.")
else:
    logger.info(f"REDIS_URI get successfully.")

if not S3_AK or not S3_SK or not S3_ENDPOINT or not S3_REGION or not S3_BUCKET_NAME:
    logger.warning("S3 configuration is not set. s3 functionality will be disabled.")
else:
    logger.info("S3 configuration get successfully.")