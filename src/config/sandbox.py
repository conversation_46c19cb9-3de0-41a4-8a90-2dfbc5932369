import os
import logging
from dotenv import load_dotenv

logger = logging.getLogger(__name__)

load_dotenv(override=True)

SANDBOX_SNAPSHOT = os.getenv("SANDBOX_SNAPSHOT") if os.getenv("SANDBOX_SNAPSHOT") else "cxy0317/playwright-vnc-mcp:0.0.5"
DAYTONA_API_KEY = os.getenv("DAYTONA_API_KEY")

if not DAYTONA_API_KEY:
    logger.warning("DAYTONA_API_KEY environment variable is not set. Sandbox functionality will be disabled.")
else:
    logger.info(f"DAYTONA_API_KEY get successfully.")