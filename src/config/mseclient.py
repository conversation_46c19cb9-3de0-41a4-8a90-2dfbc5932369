import os
import sys
import io
from typing import List
from dotenv import load_dotenv, dotenv_values

from alibabacloud_mse20190531.client import Client as Mse20190531Client
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_mse20190531 import models as mse_20190531_models
from alibabacloud_tea_util import models as util_models
from alibabacloud_tea_util.client import Client as UtilClient
import yaml

# Load .env file at program startup
load_dotenv()


class MseHandler:
    def __init__(self):
        self.client = self.create_client()

    @staticmethod
    def create_client() -> Mse20190531Client:
        # It's recommended to get AccessKey from environment variables to avoid hardcoding.
        access_key_id = os.environ.get('ALIYUN_ACCESS_KEY_ID')
        access_key_secret = os.environ.get('ALIYUN_ACCESS_KEY_SECRET')

        if not access_key_id or not access_key_secret:
            print("Error: Environment variables ALIYUN_ACCESS_KEY_ID and ALIYUN_ACCESS_KEY_SECRET are not set.")
            print("Please configure them in the .env file or directly in your environment.")
            sys.exit(1)

        config = open_api_models.Config(
            access_key_id=access_key_id,
            access_key_secret=access_key_secret
        )
        # The endpoint of the service
        config.endpoint = 'mse.cn-hangzhou.aliyuncs.com'
        return Mse20190531Client(config)

    def list_nacos_configs(self, instance_id: str):
        try:
            list_nacos_configs_request = mse_20190531_models.ListNacosConfigsRequest(
                instance_id=instance_id,
                page_num=1,
                page_size=10
            )
            runtime = util_models.RuntimeOptions()
            print("Listing Nacos configurations:")
            response = self.client.list_nacos_configs_with_options(list_nacos_configs_request, runtime)

            # Print response
            print("Configuration list:")
            if response and response.body and response.body.configurations:
                for config in response.body.configurations:
                    print(f"  DataId: {config.data_id}, Group: {config.group}")
            else:
                print("  No configurations found or response is empty.")
                print(f"  Response: {UtilClient.to_jsonstring(response.body)}")

        except Exception as error:
            print(f"Error when getting configuration list: {error}")
            if hasattr(error, 'data') and error.data.get("Recommend"):
                print(error.data.get("Recommend"))

    def get_nacos_config(self, instance_id: str, data_id: str, group: str):
        try:
            get_nacos_config_request = mse_20190531_models.GetNacosConfigRequest(
                instance_id=instance_id,
                data_id=data_id,
                group=group
            )
            runtime = util_models.RuntimeOptions()
            print(f"\nGetting configuration details (Instance: {instance_id}, DataId: {data_id}, Group: {group}):")
            response = self.client.get_nacos_config_with_options(get_nacos_config_request, runtime)

            # Print response
            #print("Configuration details:")
            #print(f"  {UtilClient.to_jsonstring(response.body)}")
            
            if response and response.body and response.body.configuration:
                return response.body.configuration.content
            return None

        except Exception as error:
            print(f"Error when getting configuration details: {error}")
            if hasattr(error, 'data') and error.data.get("Recommend"):
                print(error.data.get("Recommend"))
            return None

    def load_config_to_env(self, instance_id: str, data_id: str, group: str):
        """
        Fetches Nacos configuration and loads its variables into environment variables.
        """
        print(f"\nLoading configuration from Nacos (DataId: {data_id}, Group: {group}) into environment variables...")
        config_content = self.get_nacos_config(instance_id, data_id, group)

        if config_content:
            try:
                # Use python-dotenv to parse variables from a string
                file_like_object = io.StringIO(config_content)
                loaded_vars = dotenv_values(stream=file_like_object)
                
                if not loaded_vars:
                    print("  Configuration content is empty or malformed, no variables were loaded.")
                    return

                os.environ.update(loaded_vars)
                
                print("  Successfully loaded the following environment variables:")
                for key in loaded_vars:
                    print(f"    - {key}")

            except Exception as e:
                print(f"  Error parsing or loading configuration content: {e}")
        else:
            print("  Failed to get configuration content, cannot load into environment variables.")


if __name__ == '__main__':
    instance_id_to_check = os.environ.get('MSE_INSTANCE_ID')
    if not instance_id_to_check:
        print("Error: Environment variable MSE_INSTANCE_ID is not set.")
        print("Please configure it in the .env file or directly in your environment.")
        sys.exit(1)

    handler = MseHandler()
    handler.list_nacos_configs(instance_id=instance_id_to_check)

    # === Example Usage ===
    # Get a DataId and Group from the list above to load into environment variables
    example_data_id = "oneport_ai"
    example_group = "test"
    handler.load_config_to_env(
        instance_id=instance_id_to_check,
        data_id=example_data_id,
        group=example_group
    )
    model_config = handler.get_nacos_config(os.environ.get('MSE_INSTANCE_ID'), "oneport_ai", "model")
    print(model_config)
    model_config_dict = yaml.safe_load(model_config)
    print(model_config_dict)
    """
        # Verify if an environment variable has been loaded (assuming "NEW_VARIABLE" is in the config)
    print("\nVerifying loaded environment variables:")
    new_var = os.environ.get("NEW_VARIABLE")
    if new_var:
        print(f"  Successfully read NEW_VARIABLE: {new_var}")
    else:
        print("  Environment variable named NEW_VARIABLE not found.")
    """

