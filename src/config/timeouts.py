# 超时配置
import os

# MCP 相关超时设置
MCP_CONNECTION_TIMEOUT = int(os.getenv("MCP_CONNECTION_TIMEOUT", "30"))  # MCP 连接超时（秒）
MCP_READ_TIMEOUT = int(os.getenv("MCP_READ_TIMEOUT", "60"))  # MCP 读取超时（秒）

# 流式响应超时设置
STREAM_TIMEOUT = int(os.getenv("STREAM_TIMEOUT", "300"))  # 流式响应超时（秒）
STREAM_KEEPALIVE_INTERVAL = int(os.getenv("STREAM_KEEPALIVE_INTERVAL", "30"))  # 保活间隔（秒）

# 代理执行超时设置
AGENT_EXECUTION_TIMEOUT = int(os.getenv("AGENT_EXECUTION_TIMEOUT", "180"))  # 代理执行超时（秒）
AGENT_RECURSION_LIMIT = int(os.getenv("AGENT_RECURSION_LIMIT", "100"))  # 代理递归限制

# 工具调用超时设置
TOOL_CALL_TIMEOUT = int(os.getenv("TOOL_CALL_TIMEOUT", "120"))  # 工具调用超时（秒）

# 数据库操作超时设置
DB_OPERATION_TIMEOUT = int(os.getenv("DB_OPERATION_TIMEOUT", "30"))  # 数据库操作超时（秒） 