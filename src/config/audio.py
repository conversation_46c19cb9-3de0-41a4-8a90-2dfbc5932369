import os
import enum
import logging
from dotenv import load_dotenv

logger = logging.getLogger(__name__)

load_dotenv(override=True)

# postgres configuration
DASHSCOPE_API_KEY = os.getenv("DASHSCOPE_API_KEY")
DASHSCOPE_VOICE_TYPE = os.getenv("DASHSCOPE_VOICE_TYPE", "longhan_v2")

# Validate POSTGRES_URI
if not DASHSCOPE_API_KEY:
    logger.error("DASHSCOPE_API_KEY environment variable is not set. Audio functionality will be disabled.")
else:
    # Log the first part of the URI for debugging (hide credentials)
    logger.info(f"DASHSCOPE_API_KEY get successfully.")

if not DASHSCOPE_VOICE_TYPE:
    logger.warning("DASHSCOPE_VOICE_TYPE environment variable is not set. voice type will be set to longhan_v2.")
else:
    logger.info(f"DASHSCOPE_VOICE_TYPE get successfully.")