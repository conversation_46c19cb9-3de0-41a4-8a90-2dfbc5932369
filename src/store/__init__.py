"""
Database storage module for OnePort AI.
"""
import logging

from src.store.table import create_tables, ConversationTable, MessageTable
from src.store.storage import S3Storage, create_s3_storage
from src.config.store import POSTGRES_URI, REDIS_URI, S3_AK, S3_SK, S3_ENDPOINT, S3_BUCKET_NAME, S3_REGION
from langgraph.checkpoint.postgres import PostgresSaver
from langgraph.checkpoint.redis import RedisSaver

logger = logging.getLogger(__name__)

# 全局S3存储实例
s3_storage = None

# Check if POSTGRES_URI is set
if POSTGRES_URI:
    try:
        # Mask the connection string for logging
        masked_uri = POSTGRES_URI.split("@")
        if len(masked_uri) > 1:
            masked_uri = f"...@{masked_uri[1]}"
        else:
            masked_uri = "..."

        logger.info(f"Initializing database with URI: {masked_uri}")
        create_tables(POSTGRES_URI)
        logger.info("Database tables created successfully")
    except Exception as e:
        logger.error(f"Error initializing database: {str(e)}")
        # Log the full error traceback for debugging
        import traceback
        logger.error(traceback.format_exc())
else:
    logger.warning("POSTGRES_URI is not set. Database initialization skipped.")

# 创建redis checkpoint
if REDIS_URI:
    with RedisSaver.from_conn_string(REDIS_URI) as memory:
        memory.setup()

# 创建全局S3存储实例
if S3_AK and S3_SK and S3_ENDPOINT and S3_BUCKET_NAME and S3_REGION:
    try:
        s3_storage = create_s3_storage(
            bucket_name=S3_BUCKET_NAME,
            aws_access_key_id=S3_AK,
            aws_secret_access_key=S3_SK,
            region_name=S3_REGION,
            endpoint_url=S3_ENDPOINT
        )
        logger.info(f"Global S3 storage initialized successfully with bucket: {S3_BUCKET_NAME}")
    except Exception as e:
        logger.error(f"Error initializing S3 storage: {str(e)}")
        # Log the full error traceback for debugging
        import traceback
        logger.error(traceback.format_exc())
        s3_storage = None
else:
    logger.warning("S3 configuration is incomplete. Global S3 storage initialization skipped.")
    s3_storage = None


__all__ = ["create_tables", "ConversationTable", "MessageTable", "s3_storage", "S3Storage", "create_s3_storage"]
