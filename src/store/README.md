# S3存储服务

这个模块提供了一个简单易用的S3存储服务类，支持文件的上传、下载和删除操作。

## 功能特性

- ✅ **文件上传**: 支持上传本地文件到S3存储桶
- ✅ **文件下载**: 支持从S3下载文件到本地
- ✅ **文件删除**: 支持删除S3中的文件
- ✅ **文件检查**: 检查文件是否存在
- ✅ **文件信息**: 获取文件的详细信息（大小、修改时间等）
- ✅ **文件列表**: 列出存储桶中的文件
- ✅ **错误处理**: 完善的错误处理和日志记录
- ✅ **兼容性**: 支持MinIO等S3兼容的存储服务
- ✅ **环境变量**: 支持从环境变量读取配置

## 安装依赖

确保已安装 `boto3` 库：

```bash
pip install boto3
```

或者使用项目的依赖管理：

```bash
uv sync
```

## 快速开始

### 1. 基本使用

```python
from src.store.storage import S3Storage

# 创建S3存储实例
storage = S3Storage(
    bucket_name="your-bucket-name",
    aws_access_key_id="your-access-key",
    aws_secret_access_key="your-secret-key",
    region_name="us-east-1"
)

# 上传文件
success = storage.upload_file(
    local_file_path="/path/to/local/file.txt",
    s3_key="folder/file.txt",
    metadata={"author": "oneport-ai"},
    content_type="text/plain"
)

# 下载文件
success = storage.download_file(
    s3_key="folder/file.txt",
    local_file_path="/path/to/download/file.txt"
)

# 删除文件
success = storage.delete_file("folder/file.txt")
```

### 2. 使用工厂函数

```python
from src.store.storage import create_s3_storage

storage = create_s3_storage(
    bucket_name="your-bucket-name",
    aws_access_key_id="your-access-key",
    aws_secret_access_key="your-secret-key",
    region_name="ap-southeast-1"
)
```

### 3. 使用环境变量

设置环境变量：

```bash
export AWS_S3_BUCKET_NAME="your-bucket-name"
export AWS_ACCESS_KEY_ID="your-access-key"
export AWS_SECRET_ACCESS_KEY="your-secret-key"
export AWS_DEFAULT_REGION="us-east-1"
```

然后创建实例：

```python
from src.store.storage import create_s3_storage_from_env

storage = create_s3_storage_from_env()
```

### 4. 使用全局S3存储实例

OnePort AI提供了一个全局的S3存储实例，可以在整个应用中使用：

```python
from src.store import s3_storage

# 检查全局实例是否可用
if s3_storage is not None:
    # 直接使用全局实例
    success = s3_storage.upload_text(
        text="Hello, World!",
        s3_key="test/hello.txt"
    )
    
    # 下载文本
    content = s3_storage.download_text("test/hello.txt")
    print(content)
    
    # 上传JSON数据
    data = {"message": "Hello from OnePort AI"}
    s3_storage.upload_json(data, "test/data.json")
    
    # 下载JSON数据
    loaded_data = s3_storage.download_json("test/data.json")
else:
    print("全局S3存储实例未初始化，请检查环境变量配置")
```

**环境变量配置：**

全局S3存储实例需要以下环境变量：

```bash
export S3_AK="your-access-key"
export S3_SK="your-secret-key"
export S3_ENDPOINT="https://s3.amazonaws.com"  # 或MinIO端点
export S3_BUCKET_NAME="your-bucket-name"
export S3_REGION="us-east-1"
```

### 5. MinIO兼容性

```python
storage = S3Storage(
    bucket_name="test-bucket",
    aws_access_key_id="minio-access-key",
    aws_secret_access_key="minio-secret-key",
    region_name="us-east-1",
    endpoint_url="http://localhost:9000"  # MinIO端点
)
```

## API参考

### S3Storage类

#### 构造函数

```python
S3Storage(
    bucket_name: str,
    aws_access_key_id: Optional[str] = None,
    aws_secret_access_key: Optional[str] = None,
    region_name: str = "us-east-1",
    endpoint_url: Optional[str] = None
)
```

**参数：**
- `bucket_name`: S3存储桶名称
- `aws_access_key_id`: AWS访问密钥ID（可选，使用环境变量）
- `aws_secret_access_key`: AWS访问密钥（可选，使用环境变量）
- `region_name`: AWS区域名称，默认为"us-east-1"
- `endpoint_url`: 自定义端点URL，用于MinIO等兼容服务

#### 主要方法

##### upload_file()

```python
upload_file(
    local_file_path: str,
    s3_key: str,
    metadata: Optional[Dict[str, str]] = None,
    content_type: Optional[str] = None
) -> bool
```

上传本地文件到S3。

**参数：**
- `local_file_path`: 本地文件路径
- `s3_key`: S3中的对象键（文件路径）
- `metadata`: 文件元数据（可选）
- `content_type`: 文件内容类型（可选）

**返回：** 上传是否成功

##### download_file()

```python
download_file(
    s3_key: str,
    local_file_path: str,
    create_dirs: bool = True
) -> bool
```

从S3下载文件到本地。

**参数：**
- `s3_key`: S3中的对象键（文件路径）
- `local_file_path`: 本地保存路径
- `create_dirs`: 是否自动创建目录

**返回：** 下载是否成功

##### delete_file()

```python
delete_file(s3_key: str) -> bool
```

从S3删除文件。

**参数：**
- `s3_key`: S3中的对象键（文件路径）

**返回：** 删除是否成功

##### file_exists()

```python
file_exists(s3_key: str) -> bool
```

检查S3中文件是否存在。

##### get_file_info()

```python
get_file_info(s3_key: str) -> Optional[Dict[str, Any]]
```

获取S3文件的详细信息。

**返回：** 包含文件大小、修改时间、内容类型等信息的字典

##### list_files()

```python
list_files(prefix: str = "", max_keys: int = 1000) -> list
```

列出S3存储桶中的文件。

**参数：**
- `prefix`: 文件前缀过滤
- `max_keys`: 最大返回文件数量

**返回：** 文件信息列表

##### get_download_url()

```python
get_download_url(s3_key: str, expires_in: int = 3600) -> Optional[str]
```

生成文件的预签名下载URL。

**参数：**
- `s3_key`: S3中的对象键（文件路径）
- `expires_in`: URL过期时间（秒），默认1小时

**返回：** 预签名下载URL，失败时返回None

##### get_upload_url()

```python
get_upload_url(s3_key: str, expires_in: int = 3600, content_type: Optional[str] = None) -> Optional[str]
```

生成文件的预签名上传URL。

**参数：**
- `s3_key`: S3中的对象键（文件路径）
- `expires_in`: URL过期时间（秒），默认1小时
- `content_type`: 文件内容类型（可选）

**返回：** 预签名上传URL，失败时返回None

##### get_public_url()

```python
get_public_url(s3_key: str) -> str
```

获取文件的公共访问URL（适用于公共存储桶）。

**参数：**
- `s3_key`: S3中的对象键（文件路径）

**返回：** 公共访问URL

##### get_file_urls()

```python
get_file_urls(s3_key: str, expires_in: int = 3600) -> Dict[str, Optional[str]]
```

获取文件的所有类型URL。

**参数：**
- `s3_key`: S3中的对象键（文件路径）
- `expires_in`: 预签名URL过期时间（秒），默认1小时

**返回：** 包含各种类型URL的字典

## 环境变量配置

支持以下环境变量：

| 环境变量 | 描述 | 是否必需 |
|---------|------|----------|
| `AWS_S3_BUCKET_NAME` | S3存储桶名称 | 是 |
| `AWS_ACCESS_KEY_ID` | AWS访问密钥ID | 否* |
| `AWS_SECRET_ACCESS_KEY` | AWS访问密钥 | 否* |
| `AWS_DEFAULT_REGION` | AWS区域名称 | 否 |
| `AWS_ENDPOINT_URL` | 自定义端点URL | 否 |

*注：如果使用IAM角色，则不需要设置访问密钥

## 错误处理

所有方法都包含完善的错误处理：

- **凭证错误**: 当AWS凭证无效或缺失时抛出 `NoCredentialsError`
- **存储桶错误**: 当存储桶不存在或无权访问时抛出 `ClientError`
- **文件错误**: 当文件不存在或操作失败时返回 `False` 并记录日志
- **网络错误**: 自动重试和错误日志记录

## 示例代码

查看 `examples/s3_storage_example.py` 获取完整的使用示例。

## 测试

运行单元测试：

```bash
python -m pytest tests/test_s3_storage.py -v
```

## 注意事项

1. **权限配置**: 确保AWS用户或角色具有必要的S3权限
2. **区域设置**: 确保区域设置与存储桶所在区域一致
3. **网络连接**: 确保网络连接稳定，特别是在上传大文件时
4. **成本控制**: 注意S3的使用成本，特别是频繁的API调用
5. **安全性**: 不要在代码中硬编码访问密钥，使用环境变量或IAM角色

## 许可证

本项目使用与主项目相同的许可证。 