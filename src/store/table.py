"""
PostgreSQL table definitions for the OnePort AI application.
"""
import json
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any
from src.config.store import POSTGRES_URI

import psycopg


# SQL statements for creating tables
CREATE_CONVERSATION_TABLE = """
CREATE TABLE IF NOT EXISTS conversation (
    id VARCHAR(64) PRIMARY KEY,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    title TEXT NULL,
    user_id UUID NULL,
    sandbox_id TEXT NULL,
    conversation_type VARCHAR(50) NULL
);
"""

CREATE_MESSAGE_TABLE = """
CREATE TABLE IF NOT EXISTS message (
    id VARCHAR(64) PRIMARY KEY,
    conversation_id VARCHAR(64) NOT NULL,
    content TEXT NULL,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    parent_message_id VARCHAR(64),
    role VARCHAR(50) NOT NULL,
    tool_calls JSONB,
    file_paths TEXT NULL,
    FOREIGN KEY (conversation_id) REFERENCES conversation(id) ON DELETE CASCADE
);
"""


def create_tables(db_url: str) -> None:
    """
    Create all necessary tables in the PostgreSQL database.

    Args:
        db_url: PostgreSQL connection string
    """
    import logging
    logger = logging.getLogger(__name__)

    if not db_url:
        logger.error("Database URL is empty or None")
        return

    logger.info(f"Connecting to database with URL: {db_url[:10]}...")

    # Use a direct connection instead of a pool for initialization
    try:
        # Connect directly to the database
        conn = psycopg.connect(db_url)
        conn.autocommit = False

        try:
            # Test the connection
            with conn.cursor() as cur:
                cur.execute("SELECT 1")
                result = cur.fetchone()
                logger.info(f"Database connection test: {result}")

            # Create conversation table
            with conn.cursor() as cur:
                try:
                    logger.info("Creating conversation table...")
                    cur.execute(CREATE_CONVERSATION_TABLE)
                    conn.commit()
                    logger.info("Created conversation table successfully")
                except Exception as e:
                    conn.rollback()
                    logger.error(f"Failed to create conversation table: {str(e)}")
                    raise

            # Create message table
            with conn.cursor() as cur:
                try:
                    logger.info("Creating message table...")
                    cur.execute(CREATE_MESSAGE_TABLE)
                    conn.commit()
                    logger.info("Created message table successfully")
                except Exception as e:
                    conn.rollback()
                    logger.error(f"Failed to create message table: {str(e)}")
                    raise
        finally:
            # Close the connection
            conn.close()
            logger.info("Database connection closed")
    except Exception as e:
        logger.error(f"Database connection or table creation failed: {str(e)}")
        raise


class ConversationTable:
    """
    Class for interacting with the conversation table.
    """

    def __init__(self):
        """
        Initialize with a database URL.

        Args:
            db_url: PostgreSQL connection string
        """
        self.db_url = POSTGRES_URI

    def create(self, title: str, user_id: str, conversation_id: str, sandbox_id: str = '', conversation_type: str = '', status: str = 'running') -> uuid.UUID:
        """
        Create a new conversation.

        Args:
            title: Title of the conversation
            user_id: ID of the user who created the conversation

        Returns:
            UUID of the created conversation
        """

        if not user_id:
            user_id = str(uuid.uuid4())

        # Connect directly to the database
        with psycopg.connect(self.db_url) as conn:
            with conn.cursor() as cur:
                cur.execute(
                    """
                    INSERT INTO conversation (id, title, user_id, sandbox_id, conversation_type, status)
                    VALUES (%s, %s, %s, %s, %s, %s)
                    """,
                    (conversation_id, title, user_id, sandbox_id, conversation_type, status)
                )
                conn.commit()

        return conversation_id

    def get(self, conversation_id: str) -> Optional[Dict[str, Any]]:
        """
        Get a conversation by ID.

        Args:
            conversation_id: ID of the conversation to retrieve

        Returns:
            Conversation data as a dictionary, or None if not found
        """
        with psycopg.connect(self.db_url) as conn:
            with conn.cursor() as cur:
                cur.execute(
                    """
                    SELECT id, create_time, update_time, title, user_id, sandbox_id, conversation_type
                    FROM conversation
                    WHERE id = %s
                    """,
                    (conversation_id,)
                )
                result = cur.fetchone()

                if result:
                    return {
                        "id": result[0],
                        "create_time": result[1],
                        "update_time": result[2],
                        "title": result[3],
                        "user_id": result[4],
                        "sandbox_id": result[5],
                        "conversation_type": result[6]
                    }
                return None

    def update(
        self,
        conversation_id: str,
        title: Optional[str] = None,
        user_id: Optional[str] = None,
        sandbox_id: Optional[str] = None,
        conversation_type: Optional[str] = None,
        status: Optional[str] = None
    ) -> bool:
        """
        Update an existing conversation.

        Args:
            conversation_id: ID of the conversation to update
            title: New title for the conversation (optional)
            user_id: New user ID for the conversation (optional)
            sandbox_id: New sandbox ID for the conversation (optional)

        Returns:
            True if the update was successful, False otherwise
        """
        if not conversation_id:
            # Nothing to update
            return False

        # Build the update query dynamically based on what needs to be updated
        update_parts = []
        params = []

        if title is not None:
            update_parts.append("title = %s")
            params.append(title)

        if user_id is not None:
            update_parts.append("user_id = %s")
            params.append(user_id)

        if sandbox_id is not None:
            update_parts.append("sandbox_id = %s")
            params.append(sandbox_id)

        if conversation_type is not None:
            update_parts.append("conversation_type = %s")
            params.append(conversation_type)

        if status is not None:
            update_parts.append("status = %s")
            params.append(status)

        # Always update the update_time
        update_parts.append("update_time = CURRENT_TIMESTAMP")

        # Add the conversation_id as the last parameter
        params.append(conversation_id)

        # Connect to the database and execute the update
        with psycopg.connect(self.db_url) as conn:
            with conn.cursor() as cur:
                query = f"""
                UPDATE conversation
                SET {", ".join(update_parts)}
                WHERE id = %s
                """
                cur.execute(query, params)
                conn.commit()

                # Return True if a row was affected, False otherwise
                return cur.rowcount > 0


class MessageTable:
    """
    Class for interacting with the message table.
    """

    def __init__(self):
        """
        Initialize with a database URL.

        Args:
            db_url: PostgreSQL connection string
        """
        self.db_url = POSTGRES_URI

    def create(
        self,
        message_id: str,
        conversation_id: str,
        content: str,
        role: str,
        parent_message_id: Optional[str] = None,
        tool_calls: Optional[List[Dict[str, Any]]] = None,
        file_paths: Optional[List[str]] = None
    ) -> uuid.UUID:
        """
        Create a new message.

        Args:
            conversation_id: ID of the conversation
            content: Content of the message
            role: Role of the message sender (e.g., 'user', 'assistant', 'system')
            parent_message_id: ID of the parent message (optional)
            tool_calls: List of tool calls (optional)

        Returns:
            UUID of the created message
        """
        tool_calls_json = json.dumps(tool_calls) if tool_calls else None

        # Connect directly to the database
        with psycopg.connect(self.db_url) as conn:
            with conn.cursor() as cur:
                cur.execute(
                    """
                    INSERT INTO message (
                        id, conversation_id, content, parent_message_id, role, tool_calls, file_paths
                    )
                    VALUES (%s, %s, %s, %s, %s, %s, %s)
                    """,
                    (message_id, conversation_id, content, parent_message_id, role, tool_calls_json, file_paths)
                )
                conn.commit()

        return message_id

    def update(
        self,
        message_id: str,
        content: Optional[str] = None,
        tool_calls: Optional[List[Dict[str, Any]]] = None
    ) -> bool:
        """
        Update an existing message.

        Args:
            message_id: ID of the message to update
            content: New content for the message (optional)
            tool_calls: New tool calls for the message (optional)

        Returns:
            True if the update was successful, False otherwise
        """
        if content is None and tool_calls is None:
            # Nothing to update
            return False

        # Build the update query dynamically based on what needs to be updated
        update_parts = []
        params = []

        if content is not None:
            update_parts.append("content = %s")
            params.append(content)

        if tool_calls is not None:
            update_parts.append("tool_calls = %s")
            tool_calls_json = json.dumps(tool_calls) if tool_calls else None
            params.append(tool_calls_json)

        # Always update the update_time
        update_parts.append("update_time = CURRENT_TIMESTAMP")

        # Add the message_id as the last parameter
        params.append(message_id)

        # Connect to the database and execute the update
        with psycopg.connect(self.db_url) as conn:
            with conn.cursor() as cur:
                query = f"""
                UPDATE message
                SET {", ".join(update_parts)}
                WHERE id = %s
                """
                cur.execute(query, params)
                conn.commit()

                # Return True if a row was affected, False otherwise
                return cur.rowcount > 0

    def get(self, message_id: str) -> Optional[Dict[str, Any]]:
        """
        Get a message by ID.

        Args:
            message_id: ID of the message to retrieve

        Returns:
            Message data as a dictionary, or None if not found
        """
        with psycopg.connect(self.db_url) as conn:
            with conn.cursor() as cur:
                cur.execute(
                    """
                    SELECT id, conversation_id, content, create_time, update_time,
                           parent_message_id, role, tool_calls
                    FROM message
                    WHERE id = %s
                    """,
                    (message_id,)
                )
                result = cur.fetchone()

                if result:
                    return {
                        "id": result[0],
                        "conversation_id": result[1],
                        "content": result[2],
                        "create_time": result[3],
                        "update_time": result[4],
                        "parent_message_id": result[5],
                        "role": result[6],
                        "tool_calls": json.loads(result[7]) if result[7] else None,
                        "file_paths": json.loads(result[8]) if result[8] else None
                    }
                return None