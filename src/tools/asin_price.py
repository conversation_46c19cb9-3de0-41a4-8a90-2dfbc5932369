import logging
from typing import Annotated

from langchain_core.tools import tool
from .decorators import log_io

from src.asins.asiner import Asiner, AsinerEngine

import os
from dotenv import load_dotenv

load_dotenv(override=True)

logger = logging.getLogger(__name__)


@tool
@log_io
def get_asin_price_tool(
    asins: Annotated[list[str], "The asins to get price."],
) -> str:
    """Use this to get the price of the amazon asins."""
    try:
        asiner = Asiner(engine=AsinerEngine.JUNGLE_SCOUT)
        price = asiner.get(asins)
        
        return price
    except BaseException as e:
        error_msg = f"Failed to get asin price. Error: {repr(e)}"
        logger.error(error_msg)
        return error_msg
