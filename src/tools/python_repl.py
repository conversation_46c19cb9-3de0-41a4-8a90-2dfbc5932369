import logging
import re
import os
import subprocess
import tempfile
from typing import Annotated
from langchain_core.tools import tool
from .decorators import log_io

logger = logging.getLogger(__name__)


def clean_code_input(code: str) -> str:
    """
    清理代码输入，移除markdown代码块标记
    
    Args:
        code: 可能包含markdown格式的代码字符串
        
    Returns:
        清理后的纯Python代码字符串
    """
    # 移除开头和结尾的markdown代码块标记
    # 匹配 ```python 或 ``` 开头，``` 结尾的代码块
    pattern = r'^```(?:python)?\s*\n(.*?)\n```$'
    match = re.match(pattern, code.strip(), re.DOTALL)
    
    if match:
        # 如果匹配到markdown代码块，返回其中的代码内容
        return match.group(1)
    else:
        # 如果没有匹配到，返回原始代码
        return code


@tool
@log_io
def python_repl_tool(
    code: Annotated[
        str, "The python code to execute to do further analysis or calculation."
    ],
):
    """Use this to execute python code and do data analysis or calculation. If you want to see the output of a value,
    you should print it out with `print(...)`. This is visible to the user."""
    if not isinstance(code, str):
        error_msg = f"Invalid input: code must be a string, got {type(code)}"
        logger.error(error_msg)
        return f"Error executing code:\n```python\n{code}\n```\nError: {error_msg}"

    # 清理代码输入，移除可能的markdown格式
    cleaned_code = clean_code_input(code)
    logger.info(f"Original code length: {len(code)}, Cleaned code length: {len(cleaned_code)}")

    logger.info("Executing Python code")
    try:
        # 获取项目根目录
        current_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        temp_dir = os.path.join(current_dir, 'temp')
        
        # 确保临时目录存在
        if not os.path.exists(temp_dir):
            os.makedirs(temp_dir)
        
        # 在项目目录下创建临时Python文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False, dir=temp_dir) as temp_file:
            temp_file.write(cleaned_code)
            temp_file_path = temp_file.name
        
        logger.info(f"Created temporary file: {temp_file_path}")
        
        # 使用subprocess执行Python文件
        process = subprocess.run(
            ['python', temp_file_path],
            capture_output=True,
            text=True,
            timeout=30  # 30秒超时
        )
        
        # 清理临时文件
        os.unlink(temp_file_path)
        logger.info(f"Cleaned up temporary file: {temp_file_path}")
        
        # 获取执行结果
        stdout = process.stdout.strip() if process.stdout else ""
        stderr = process.stderr.strip() if process.stderr else ""
        
        if process.returncode != 0:
            # 执行失败
            error_msg = f"Process exited with code {process.returncode}"
            if stderr:
                error_msg += f"\nStderr: {stderr}"
            logger.error(error_msg)
            return f"Error executing code:\n```python\n{cleaned_code}\n```\nError: {error_msg}"
        
        result = stdout
        logger.info("Code execution successful")
    except subprocess.TimeoutExpired:
        # 清理临时文件
        if 'temp_file_path' in locals():
            try:
                os.unlink(temp_file_path)
            except:
                pass
        error_msg = "Code execution timed out (30 seconds)"
        logger.error(error_msg)
        return f"Error executing code:\n```python\n{cleaned_code}\n```\nError: {error_msg}"
    except BaseException as e:
        # 清理临时文件
        if 'temp_file_path' in locals():
            try:
                os.unlink(temp_file_path)
            except:
                pass
        error_msg = repr(e)
        logger.error(error_msg)
        return f"Error executing code:\n```python\n{cleaned_code}\n```\nError: {error_msg}"

    result_str = f"Successfully executed:\n```python\n{cleaned_code}\n```\nStdout: {result}"
    return result_str
