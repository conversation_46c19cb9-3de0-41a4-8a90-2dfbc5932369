# Amazon数据结构使用指南

本文档说明如何在Python工具执行时使用Amazon报告数据结构进行数据解析和分析。

## 概述

`amazon_data_structures.py` 模块提供了完整的Amazon Seller API报告数据结构定义和处理工具，包括：

- 销售报告 (GET_VENDOR_REAL_TIME_SALES_REPORT)
- 库存报告 (GET_VENDOR_REAL_TIME_INVENTORY_REPORT)
- 数据解析器和分析工具
- 便捷的数据处理函数

## 快速开始

### 基本导入

```python
from src.tools.amazon_data_structures import (
    # 数据结构
    VendorSalesReport,
    VendorInventoryReport,
    SalesRecord,
    InventoryRecord,
    
    # 解析器
    AmazonReportParser,
    
    # 便捷函数
    create_sales_report_from_json,
    create_inventory_report_from_json,
    analyze_asin_sales,
    analyze_asin_inventory
)
```

### 处理销售报告

```python
# 从JSON数据创建销售报告对象
sales_report = create_sales_report_from_json(json_data)

# 分析特定ASIN的销售数据
asin_analysis = analyze_asin_sales(sales_report, "B0123456789")

# 输出分析结果
print(f"ASIN: {asin_analysis['asin']}")
print(f"总销量: {asin_analysis['summary']['total_units']}")
print(f"总收入: ${asin_analysis['summary']['total_revenue']}")
print(f"平均价格: ${asin_analysis['summary']['average_price']}")
```

### 处理库存报告

```python
# 从JSON数据创建库存报告对象
inventory_report = create_inventory_report_from_json(json_data)

# 分析特定ASIN的库存数据
inventory_analysis = analyze_asin_inventory(inventory_report, "B0123456789")

# 输出分析结果
print(f"ASIN: {inventory_analysis['asin']}")
print(f"总库存: {inventory_analysis['summary']['total_inventory']}")
print(f"平均库存: {inventory_analysis['summary']['average_inventory']}")
```

## 完整工作流程示例

### Amazon报告处理的六步流程

```python
import json
import gzip
import requests
from io import BytesIO

# 步骤1: 创建报告请求 (使用MCP工具)
# report_id = createAnalyticsReport(...)

# 步骤2: 查询报告状态 (使用MCP工具)
# status = getReport(report_id)

# 步骤3: 获取报告下载URL (使用MCP工具)
# document_url = getReportDocument(report_id)

# 步骤4: 下载报告文档 (使用Python)
response = requests.get(document_url)
print(f"下载状态: {response.status_code}")

# 步骤5: 解压缩报告文件 (使用Python)
with gzip.open(BytesIO(response.content), 'rt') as f:
    json_content = f.read()
print("报告文件解压完成")

# 步骤6: 解析和分析数据 (使用数据结构)
try:
    # 解析销售报告
    sales_report = create_sales_report_from_json(json_content)
    
    # 分析目标ASIN
    target_asin = "B0123456789"
    analysis = analyze_asin_sales(sales_report, target_asin)
    
    if analysis['summary']['record_count'] > 0:
        print(f"✓ 成功分析ASIN {target_asin}")
        print(f"  销售数据: {analysis['summary']}")
        
        # 详细记录
        for record in analysis['detailed_records']:
            print(f"  {record.startTime[:10]}: {record.orderedUnits}件, ${record.orderedRevenue}")
    else:
        print(f"⚠ 未找到ASIN {target_asin}的数据")
        
except ValueError as e:
    print(f"✗ 数据解析失败: {e}")
```

## 数据结构详解

### 销售报告结构

```python
@dataclass
class VendorSalesReport:
    reportSpecification: ReportSpecification  # 报告规格
    reportData: List[SalesRecord]             # 销售记录列表

@dataclass
class SalesRecord:
    startTime: str           # 开始时间
    endTime: str            # 结束时间
    asin: str               # 产品ASIN
    orderedUnits: int       # 订购数量
    orderedRevenue: int     # 订购收入
```

### 库存报告结构

```python
@dataclass
class VendorInventoryReport:
    reportSpecification: ReportSpecification  # 报告规格
    reportData: List[InventoryRecord]         # 库存记录列表

@dataclass
class InventoryRecord:
    startTime: str                    # 开始时间
    endTime: str                     # 结束时间
    asin: str                        # 产品ASIN
    highlyAvailableInventory: int    # 高可用库存
```

## 高级功能

### 自定义数据过滤

```python
# 使用解析器进行自定义过滤
parser = AmazonReportParser()

# 按ASIN过滤
filtered_records = parser.filter_by_asin(sales_report.reportData, "B0123456789")

# 生成销售摘要
summary = parser.format_sales_summary(filtered_records)
print(f"销售摘要: {summary}")
```

### 错误处理

```python
try:
    # 解析可能有问题的数据
    report = create_sales_report_from_json(json_data)
except ValueError as e:
    print(f"数据解析错误: {e}")
    # 处理错误情况
except Exception as e:
    print(f"未知错误: {e}")
    # 处理其他异常
```

### 批量处理多个ASIN

```python
def analyze_multiple_asins(sales_report, asin_list):
    """批量分析多个ASIN的销售数据"""
    results = {}
    
    for asin in asin_list:
        try:
            analysis = analyze_asin_sales(sales_report, asin)
            results[asin] = analysis['summary']
        except Exception as e:
            results[asin] = {"error": str(e)}
    
    return results

# 使用示例
asin_list = ["B0123456789", "B0987654321", "B0555666777"]
batch_results = analyze_multiple_asins(sales_report, asin_list)

for asin, result in batch_results.items():
    if "error" in result:
        print(f"ASIN {asin}: 分析失败 - {result['error']}")
    else:
        print(f"ASIN {asin}: 销量 {result['total_units']}, 收入 ${result['total_revenue']}")
```

## 最佳实践

### 1. 数据验证
```python
# 在处理数据前验证报告类型
if sales_report.reportSpecification.reportType == "GET_VENDOR_REAL_TIME_SALES_REPORT":
    # 处理销售报告
    pass
else:
    print("警告: 报告类型不匹配")
```

### 2. 性能优化
```python
# 对于大量数据，使用生成器
def process_large_report(report_data):
    for record in report_data:
        # 逐条处理，避免内存溢出
        yield process_single_record(record)
```

### 3. 日志记录
```python
import logging

logger = logging.getLogger(__name__)

def safe_analyze_asin(sales_report, asin):
    """安全的ASIN分析，包含日志记录"""
    try:
        logger.info(f"开始分析ASIN: {asin}")
        analysis = analyze_asin_sales(sales_report, asin)
        logger.info(f"ASIN {asin} 分析完成，找到 {analysis['summary']['record_count']} 条记录")
        return analysis
    except Exception as e:
        logger.error(f"ASIN {asin} 分析失败: {e}")
        raise
```

## 常见问题

### Q: 如何处理空的报告数据？
A: 数据结构会自动处理空数据，返回默认值。检查 `record_count` 来确认是否有数据。

### Q: 如何处理不同时区的时间数据？
A: 时间字段以字符串形式存储，建议使用 `datetime` 模块进行时区转换。

### Q: 如何扩展数据结构支持新的报告类型？
A: 参考现有的数据结构定义，创建新的 `@dataclass` 和相应的解析方法。

## 示例文件

完整的使用示例请参考：`examples/amazon_data_usage_example.py`

运行示例：
```bash
python examples/amazon_data_usage_example.py
```

## 支持的报告类型

- ✅ GET_VENDOR_REAL_TIME_SALES_REPORT (实时销售报告)
- ✅ GET_VENDOR_REAL_TIME_INVENTORY_REPORT (实时库存报告)
- 🔄 更多报告类型正在开发中...

## 更新日志

- v1.0.0: 初始版本，支持销售和库存报告
- 计划中: 支持更多Amazon报告类型 