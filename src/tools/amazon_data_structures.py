"""
Amazon报告数据结构定义

这个模块包含了Amazon Seller API报告的数据结构定义，
用于在Python工具执行时进行数据解析和处理。
"""

from dataclasses import dataclass
from typing import List, Optional, Dict, Any
import json
from datetime import datetime
from collections import defaultdict


@dataclass
class ReportOptions:
    """报告选项"""
    currencyCode: str


@dataclass
class ReportSpecification:
    """报告规格"""
    reportType: str
    dataStartTime: str
    dataEndTime: str
    marketplaceIds: List[str]
    reportOptions: Optional[ReportOptions] = None


@dataclass
class SalesRecord:
    """销售记录"""
    startTime: str
    endTime: str
    asin: str
    orderedUnits: int
    orderedRevenue: int

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SalesRecord':
        """从字典创建SalesRecord实例"""
        return cls(
            startTime=data.get('startTime', ''),
            endTime=data.get('endTime', ''),
            asin=data.get('asin', ''),
            orderedUnits=int(data.get('orderedUnits', 0)),
            orderedRevenue=int(data.get('orderedRevenue', 0))
        )


@dataclass
class VendorSalesReport:
    """供应商销售报告"""
    reportSpecification: ReportSpecification
    reportData: List[SalesRecord]

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'VendorSalesReport':
        """从字典创建VendorSalesReport实例"""
        spec_data = data.get('reportSpecification', {})
        report_options = None
        if 'reportOptions' in spec_data:
            report_options = ReportOptions(
                currencyCode=spec_data['reportOptions'].get('currencyCode', 'USD')
            )
        
        spec = ReportSpecification(
            reportType=spec_data.get('reportType', ''),
            dataStartTime=spec_data.get('dataStartTime', ''),
            dataEndTime=spec_data.get('dataEndTime', ''),
            marketplaceIds=spec_data.get('marketplaceIds', []),
            reportOptions=report_options
        )
        
        records = [
            SalesRecord.from_dict(record) 
            for record in data.get('reportData', [])
        ]
        
        return cls(reportSpecification=spec, reportData=records)


@dataclass
class InventoryRecord:
    """库存记录"""
    startTime: str
    endTime: str
    asin: str
    highlyAvailableInventory: int

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'InventoryRecord':
        """从字典创建InventoryRecord实例"""
        return cls(
            startTime=data.get('startTime', ''),
            endTime=data.get('endTime', ''),
            asin=data.get('asin', ''),
            highlyAvailableInventory=int(data.get('highlyAvailableInventory', 0))
        )


@dataclass
class VendorInventoryReport:
    """供应商库存报告"""
    reportSpecification: ReportSpecification
    reportData: List[InventoryRecord]

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'VendorInventoryReport':
        """从字典创建VendorInventoryReport实例"""
        spec_data = data.get('reportSpecification', {})
        spec = ReportSpecification(
            reportType=spec_data.get('reportType', ''),
            dataStartTime=spec_data.get('dataStartTime', ''),
            dataEndTime=spec_data.get('dataEndTime', ''),
            marketplaceIds=spec_data.get('marketplaceIds', [])
        )
        
        records = [
            InventoryRecord.from_dict(record) 
            for record in data.get('reportData', [])
        ]
        
        return cls(reportSpecification=spec, reportData=records)


class AmazonReportParser:
    """Amazon报告解析器"""
    
    @staticmethod
    def parse_sales_report(json_data: str) -> VendorSalesReport:
        """解析销售报告JSON数据"""
        try:
            data = json.loads(json_data) if isinstance(json_data, str) else json_data
            return VendorSalesReport.from_dict(data)
        except (json.JSONDecodeError, KeyError) as e:
            raise ValueError(f"解析销售报告数据失败: {e}")
    
    @staticmethod
    def parse_inventory_report(json_data: str) -> VendorInventoryReport:
        """解析库存报告JSON数据"""
        try:
            data = json.loads(json_data) if isinstance(json_data, str) else json_data
            return VendorInventoryReport.from_dict(data)
        except (json.JSONDecodeError, KeyError) as e:
            raise ValueError(f"解析库存报告数据失败: {e}")
    
    @staticmethod
    def filter_by_asin(report_data: List[Any], target_asin: str) -> List[Any]:
        """根据ASIN过滤报告数据"""
        return [record for record in report_data if record.asin == target_asin]
    
    @staticmethod
    def format_sales_summary(sales_records: List[SalesRecord]) -> Dict[str, Any]:
        """格式化销售数据摘要"""
        if not sales_records:
            return {
                "total_units": 0,
                "total_revenue": 0,
                "average_price": 0,
                "record_count": 0
            }
        
        total_units = sum(record.orderedUnits for record in sales_records)
        total_revenue = sum(record.orderedRevenue for record in sales_records)
        average_price = total_revenue / total_units if total_units > 0 else 0
        
        return {
            "total_units": total_units,
            "total_revenue": total_revenue,
            "average_price": round(average_price, 2),
            "record_count": len(sales_records)
        }
    
    @staticmethod
    def format_inventory_summary(inventory_records: List[InventoryRecord]) -> Dict[str, Any]:
        """格式化库存数据摘要"""
        if not inventory_records:
            return {
                "average_inventory": 0,
                "record_count": 0
            }
        
        total_inventory = sum(record.highlyAvailableInventory for record in inventory_records)
        average_inventory = total_inventory / len(inventory_records) if inventory_records else 0
        
        return {
            "average_inventory": round(average_inventory, 2),
            "record_count": len(inventory_records)
        }


# 便捷函数
def create_sales_report_from_json(json_data: str) -> VendorSalesReport:
    """从JSON数据创建销售报告对象"""
    return AmazonReportParser.parse_sales_report(json_data)


def create_inventory_report_from_json(json_data: str) -> VendorInventoryReport:
    """从JSON数据创建库存报告对象"""
    return AmazonReportParser.parse_inventory_report(json_data)


def analyze_asin_sales(report: VendorSalesReport, asin: str) -> Dict[str, Any]:
    """分析特定ASIN的销售数据"""
    filtered_records = AmazonReportParser.filter_by_asin(report.reportData, asin)
    summary = AmazonReportParser.format_sales_summary(filtered_records)
    
    return {
        "asin": asin,
        "time_period": f"{report.reportSpecification.dataStartTime} - {report.reportSpecification.dataEndTime}",
        "marketplace_ids": report.reportSpecification.marketplaceIds,
        "summary": summary,
        "detailed_records": filtered_records
    }


def analyze_asin_inventory(report: VendorInventoryReport, asin: str) -> Dict[str, Any]:
    """分析特定ASIN的库存数据"""
    filtered_records = AmazonReportParser.filter_by_asin(report.reportData, asin)
    summary = AmazonReportParser.format_inventory_summary(filtered_records)
    
    return {
        "asin": asin,
        "time_period": f"{report.reportSpecification.dataStartTime} - {report.reportSpecification.dataEndTime}",
        "marketplace_ids": report.reportSpecification.marketplaceIds,
        "summary": summary,
        "detailed_records": filtered_records
    }

# New analysis function
def analyze_daily_sales_trends(sales_records: List[SalesRecord]) -> Dict[str, Any]:
    """Analyze daily sales trends"""
    daily_data = {}
    
    for record in sales_records:
        date = record.startTime.split('T')[0]  # Extract date part
        if date not in daily_data:
            daily_data[date] = {"units": 0, "revenue": 0}
        
        daily_data[date]["units"] += record.orderedUnits
        daily_data[date]["revenue"] += record.orderedRevenue
    
    # Calculate statistical data
    daily_revenues = [data["revenue"] for data in daily_data.values()]
    daily_units = [data["units"] for data in daily_data.values()]
    
    total_days = len(daily_data)
    total_revenue = sum(daily_revenues)
    total_units = sum(daily_units)
    
    return {
        "daily_breakdown": daily_data,
        "total_revenue": total_revenue,
        "total_units": total_units,
        "average_daily_revenue": round(total_revenue / total_days, 2) if total_days > 0 else 0,
        "average_daily_units": round(total_units / total_days, 2) if total_days > 0 else 0,
        "max_daily_revenue": max(daily_revenues) if daily_revenues else 0,
        "min_daily_revenue": min(daily_revenues) if daily_revenues else 0,
        "max_daily_units": max(daily_units) if daily_units else 0,
        "min_daily_units": min(daily_units) if daily_units else 0,
        "average_selling_price": round(total_revenue / total_units, 2) if total_units > 0 else 0
    }


def analyze_inventory_trends(inventory_records: List[InventoryRecord]) -> Dict[str, Any]:
    """Analyze inventory trends"""
    daily_inventory = {}
    
    for record in inventory_records:
        date = record.startTime.split('T')[0]  # Extract date part
        daily_inventory[date] = record.highlyAvailableInventory
    
    # Calculate inventory changes
    inventory_values = list(daily_inventory.values())
    daily_changes = []
    
    for i in range(1, len(inventory_values)):
        change = inventory_values[i] - inventory_values[i-1]
        daily_changes.append(change)
    
    return {
        "daily_inventory": daily_inventory,
        "max_inventory": max(inventory_values) if inventory_values else 0,
        "min_inventory": min(inventory_values) if inventory_values else 0,
        "average_inventory": round(sum(inventory_values) / len(inventory_values), 2) if inventory_values else 0,
        "daily_changes": daily_changes,
        "total_inventory_change": sum(daily_changes) if daily_changes else 0,
        "average_daily_change": round(sum(daily_changes) / len(daily_changes), 2) if daily_changes else 0,
        "inventory_trend": "increasing" if sum(daily_changes) > 0 else "decreasing" if sum(daily_changes) < 0 else "stable"
    }


def generate_comprehensive_analysis(asin: str, sales_records: List[SalesRecord], inventory_records: List[InventoryRecord]) -> Dict[str, Any]:
    """Generate comprehensive analysis report"""
    sales_analysis = analyze_daily_sales_trends(sales_records)
    inventory_analysis = analyze_inventory_trends(inventory_records) if inventory_records else None
    
    # Identify peak sales period
    daily_sales = sales_analysis["daily_breakdown"]
    peak_sales_day = max(daily_sales.keys(), key=lambda x: daily_sales[x]["revenue"]) if daily_sales else None
    
    # Inventory alerts
    inventory_alerts = []
    if inventory_analysis:
        if inventory_analysis["min_inventory"] < 10:
            inventory_alerts.append("Low inventory warning: Minimum inventory level less than 10 units")
        if inventory_analysis["max_inventory"] > 1000:
            inventory_alerts.append("High inventory warning: Maximum inventory level exceeds 1000 units")
    
    return {
        "asin": asin,
        "sales_analysis": sales_analysis,
        "inventory_analysis": inventory_analysis,
        "peak_sales_day": peak_sales_day,
        "inventory_alerts": inventory_alerts,
        "recommendations": generate_recommendations(sales_analysis, inventory_analysis)
    }


def generate_recommendations(sales_analysis: Dict[str, Any], inventory_analysis: Optional[Dict[str, Any]]) -> List[str]:
    """Generate business recommendations"""
    recommendations = []
    
    # Business recommendations based on sales trends
    if sales_analysis["total_revenue"] > 0:
        if sales_analysis["max_daily_revenue"] > sales_analysis["average_daily_revenue"] * 2:
            recommendations.append("Found peak sales period, suggest analyzing marketing activities or external factors during the peak period")
    
    # Business recommendations based on inventory trends
    if inventory_analysis:
        if inventory_analysis["inventory_trend"] == "decreasing":
            recommendations.append("Inventory is decreasing, suggest replenishing stock in time")
        elif inventory_analysis["min_inventory"] < 20:
            recommendations.append("Low inventory level, suggest increasing safety stock")
    
    return recommendations