import logging
from typing import Annotated

from langchain_core.tools import tool
from .decorators import log_io

from src.crawler import Crawler, CrawlerEngine
import os
from dotenv import load_dotenv

load_dotenv(override=True)

logger = logging.getLogger(__name__)


@tool
@log_io
def crawl_tool(
    url: Annotated[str, "The url to crawl."],
) -> str:
    """Use this to crawl a url and get a readable content in markdown format."""
    try:
        crawl_type = os.getenv("CRAWL_TYPE", "jina").lower()
        
        # 根据crawl_type选择相应的爬虫引擎
        if crawl_type == "firecrawl":
            engine = CrawlerEngine.FIRECRAWL
        elif crawl_type == "jina":
            engine = CrawlerEngine.JINA
        else:
            # 默认使用jina，如果配置了未知类型
            logger.warning(f"Unknown CRAWL_TYPE: {crawl_type}, falling back to jina")
            engine = CrawlerEngine.JINA
        
        crawler = Crawler(engine=engine)
        article = crawler.crawl(url)
        
        return {"url": url, "crawled_content": article.to_markdown()}
    except BaseException as e:
        error_msg = f"Failed to crawl. Error: {repr(e)}"
        logger.error(error_msg)
        return error_msg
