# 超时配置指南

## 概述

为了解决 `CancelledError` 错误和提高系统稳定性，OnePort AI 提供了多种超时配置选项。这些配置可以通过环境变量进行设置。

## 环境变量配置

### MCP 相关超时设置

- `MCP_CONNECTION_TIMEOUT`: MCP 服务器连接超时（默认：30秒）
- `MCP_READ_TIMEOUT`: MCP 服务器读取超时（默认：60秒）

### 流式响应超时设置

- `STREAM_TIMEOUT`: 流式响应总超时时间（默认：300秒）
- `STREAM_KEEPALIVE_INTERVAL`: 保活信号间隔（默认：30秒）

### 代理执行超时设置

- `AGENT_EXECUTION_TIMEOUT`: 单个代理执行超时（默认：180秒）
- `AGENT_RECURSION_LIMIT`: 代理递归调用限制（默认：100）

### 工具调用超时设置

- `TOOL_CALL_TIMEOUT`: 工具调用超时（默认：120秒）

### 数据库操作超时设置

- `DB_OPERATION_TIMEOUT`: 数据库操作超时（默认：30秒）

## 配置示例

在 `.env` 文件中添加以下配置：

```bash
# 超时配置（秒）
MCP_CONNECTION_TIMEOUT=30
MCP_READ_TIMEOUT=60
STREAM_TIMEOUT=300
STREAM_KEEPALIVE_INTERVAL=30
AGENT_EXECUTION_TIMEOUT=180
AGENT_RECURSION_LIMIT=100
TOOL_CALL_TIMEOUT=120
DB_OPERATION_TIMEOUT=30
```

## 错误处理改进

### CancelledError 处理

系统现在能够优雅地处理以下情况：

1. **客户端断开连接**: 当客户端在流式响应过程中断开连接时，系统会记录日志并正确清理资源
2. **超时取消**: 当操作超时被取消时，系统会发送适当的错误信息
3. **MCP 服务器连接问题**: 改进了 MCP 服务器连接的超时处理

### 日志记录

系统会记录以下类型的事件：

- 流被取消（通常是客户端断开连接）
- MCP 连接超时
- 代理执行错误
- 工具调用失败

## 故障排除

### 常见问题

1. **频繁的 CancelledError**
   - 检查客户端是否过早断开连接
   - 增加 `STREAM_TIMEOUT` 值
   - 检查网络连接稳定性

2. **MCP 服务器连接失败**
   - 增加 `MCP_CONNECTION_TIMEOUT` 值
   - 检查 MCP 服务器是否正常运行
   - 验证 MCP 服务器配置

3. **代理执行超时**
   - 增加 `AGENT_EXECUTION_TIMEOUT` 值
   - 检查 API 密钥配置
   - 验证网络连接

### 监控建议

1. 监控日志中的超时和取消事件
2. 设置适当的告警阈值
3. 定期检查系统性能指标
4. 根据实际使用情况调整超时值

## 性能优化建议

1. **合理设置超时值**: 根据实际网络环境和服务器性能调整
2. **监控资源使用**: 确保服务器有足够的资源处理并发请求
3. **优化数据库查询**: 减少数据库操作时间
4. **使用连接池**: 对于频繁的外部服务调用，使用连接池提高效率 