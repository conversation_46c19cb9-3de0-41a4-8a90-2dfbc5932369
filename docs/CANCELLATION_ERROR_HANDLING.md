# LangGraph CancelledError 处理指南

## 问题描述

在使用 LangGraph 的 `astream` 方法时，您可能会遇到 `asyncio.exceptions.CancelledError` 异常。这个错误通常发生在以下情况：

```python
CancelledError('Cancelled by cancel scope 7f449b62f1d0', <Task cancelled name='Task-19264' coro=<AsyncExitStack.__aexit__() done, defined at /root/.local/share/uv/python/cpython-3.13.3-linux-x86_64-gnu/lib/python3.13/contextlib.py:716>>)
```

## 错误原因

### 1. 客户端断开连接
- 用户关闭浏览器或应用
- 网络连接中断
- 客户端超时

### 2. 服务器端超时
- 请求处理时间过长
- 资源限制导致的超时
- 配置的超时时间过短

### 3. 任务被显式取消
- 应用程序逻辑主动取消任务
- 系统资源不足导致的任务取消
- 信号中断（如 SIGINT, SIGTERM）

## 解决方案

### 1. 在服务器代码中添加错误处理

我们已经在 `src/server/app.py` 中添加了完整的错误处理：

```python
import asyncio

async def _astream_chat_generator(messages, thread_id, mcp_settings):
    # ... 初始化代码 ...
    
    try:
        async for agent, _, event_data in chat_graph.astream(...):
            # ... 处理逻辑 ...
            yield _make_event("message_chunk", event_stream_message)
            
    except asyncio.CancelledError:
        logger.info(f"Chat stream cancelled for thread {thread_id} - client likely disconnected")
        # 发送取消事件给客户端（如果连接仍然存在）
        try:
            yield _make_event("cancelled", {
                "conversation_id": thread_id,
                "message": "Stream was cancelled",
                "reason": "client_disconnect"
            })
        except:
            # 如果无法发送取消事件，说明客户端已经断开连接
            pass
        # 重新抛出异常以确保正确的清理
        raise
    except Exception as e:
        logger.error(f"Error in chat stream for thread {thread_id}: {e}")
        # 发送错误事件
        try:
            yield _make_event("error", {
                "conversation_id": thread_id,
                "message": f"An error occurred: {str(e)}",
                "error_type": type(e).__name__
            })
        except:
            pass
        raise
```

### 2. 使用统一的错误处理工具

我们创建了 `src/utils/error_handling.py` 模块，提供统一的错误处理功能：

```python
from src.utils.error_handling import AsyncStreamManager, with_cancellation_handling

# 使用 AsyncStreamManager
async def my_stream_function(thread_id: str):
    manager = AsyncStreamManager(thread_id, "my_operation")
    
    async for message in manager.handle_langgraph_stream(
        graph=my_graph,
        input_data=input_data,
        config=config,
        message_processor=my_message_processor
    ):
        yield message

# 使用装饰器
@handle_stream_cancellation(thread_id="example", operation_name="example_op")
async def my_generator():
    # ... 生成器逻辑 ...
    yield item
```

### 3. 客户端处理

在客户端代码中，也应该正确处理连接中断：

```javascript
// JavaScript 示例
const eventSource = new EventSource('/v1/conversation');

eventSource.addEventListener('cancelled', (event) => {
    const data = JSON.parse(event.data);
    console.log('Stream was cancelled:', data.message);
    // 处理取消逻辑
});

eventSource.addEventListener('error', (event) => {
    const data = JSON.parse(event.data);
    console.error('Stream error:', data.message);
    // 处理错误逻辑
});

// 确保在页面卸载时关闭连接
window.addEventListener('beforeunload', () => {
    eventSource.close();
});
```

## 最佳实践

### 1. 日志记录
- 记录取消事件，但不作为错误处理
- 区分客户端断开连接和真正的错误
- 使用适当的日志级别（INFO for cancellation, ERROR for real errors）

### 2. 资源清理
- 确保在异常发生时正确清理资源
- 使用 `try-finally` 或上下文管理器
- 避免资源泄漏

### 3. 客户端通知
- 在可能的情况下，向客户端发送取消或错误通知
- 使用标准化的事件格式
- 提供有意义的错误消息

### 4. 超时配置
- 设置合理的超时时间
- 考虑不同操作的复杂度
- 提供可配置的超时参数

```python
# 配置示例
TIMEOUT_CONFIG = {
    "chat": 30,  # 聊天操作 30 秒超时
    "research": 300,  # 研究操作 5 分钟超时
    "analysis": 600,  # 分析操作 10 分钟超时
}
```

## 测试

使用提供的测试脚本验证错误处理：

```bash
# 运行服务器
uvicorn src.server.app:app --host 0.0.0.0 --port 8000

# 在另一个终端运行测试
python test_cancellation_handling.py
```

测试脚本会模拟以下场景：
- 正常完成
- 客户端断开连接
- 超时处理
- 信号中断

## 监控和调试

### 1. 日志监控
```bash
# 查看取消相关的日志
grep -i "cancelled" logs/app.log

# 查看错误日志
grep -i "error" logs/app.log
```

### 2. 性能监控
- 监控平均响应时间
- 跟踪取消率
- 分析超时模式

### 3. 调试技巧
- 使用 `asyncio` 的调试模式
- 启用详细日志记录
- 使用分布式追踪工具

```python
# 启用 asyncio 调试
import asyncio
asyncio.get_event_loop().set_debug(True)

# 详细日志配置
logging.basicConfig(
    level=logging.DEBUG,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
```

## 常见问题

### Q: 为什么会频繁出现 CancelledError？
A: 检查以下几点：
- 客户端是否正确处理长时间运行的请求
- 超时配置是否合理
- 网络连接是否稳定
- 服务器资源是否充足

### Q: 如何区分正常取消和异常取消？
A: 
- 正常取消：客户端主动断开连接，记录为 INFO 级别
- 异常取消：系统错误导致的取消，记录为 ERROR 级别
- 使用上下文信息判断取消原因

### Q: 取消后如何清理资源？
A: 
- 使用 `try-finally` 确保清理代码执行
- 实现适当的上下文管理器
- 监控资源使用情况

## 相关文件

- `src/server/app.py` - 主要的错误处理实现
- `src/utils/error_handling.py` - 统一的错误处理工具
- `test_cancellation_handling.py` - 测试脚本
- `docs/CANCELLATION_ERROR_HANDLING.md` - 本文档

## 更新日志

- 2024-01-XX: 初始版本，添加基本的 CancelledError 处理
- 2024-01-XX: 添加统一的错误处理工具模块
- 2024-01-XX: 完善测试脚本和文档 