"""
Amazon数据结构使用示例

这个示例展示了如何在Python工具执行时使用Amazon报告数据结构
进行数据解析、分析和处理。
"""

import json
import gzip
import requests
from datetime import datetime, timedelta
from src.tools.amazon_data_structures import (
    VendorSalesReport,
    VendorInventoryReport,
    SalesRecord,
    InventoryRecord,
    AmazonReportParser,
    create_sales_report_from_json,
    create_inventory_report_from_json,
    analyze_asin_sales,
    analyze_asin_inventory
)


def example_sales_report_processing():
    """示例：处理销售报告数据"""
    print("=== 销售报告处理示例 ===")
    
    # 模拟从Amazon API获取的JSON数据
    sample_sales_data = {
        "reportSpecification": {
            "reportType": "GET_VENDOR_REAL_TIME_SALES_REPORT",
            "dataStartTime": "2024-01-01T00:00:00Z",
            "dataEndTime": "2024-01-31T23:59:59Z",
            "marketplaceIds": ["ATVPDKIKX0DER"],
            "reportOptions": {
                "currencyCode": "USD"
            }
        },
        "reportData": [
            {
                "startTime": "2024-01-01T00:00:00Z",
                "endTime": "2024-01-01T23:59:59Z",
                "asin": "B0123456789",
                "orderedUnits": 50,
                "orderedRevenue": 2500
            },
            {
                "startTime": "2024-01-02T00:00:00Z",
                "endTime": "2024-01-02T23:59:59Z",
                "asin": "B0123456789",
                "orderedUnits": 75,
                "orderedRevenue": 3750
            },
            {
                "startTime": "2024-01-01T00:00:00Z",
                "endTime": "2024-01-01T23:59:59Z",
                "asin": "B0987654321",
                "orderedUnits": 30,
                "orderedRevenue": 1200
            }
        ]
    }
    
    # 方法1：使用便捷函数解析
    sales_report = create_sales_report_from_json(json.dumps(sample_sales_data))
    print(f"报告类型: {sales_report.reportSpecification.reportType}")
    print(f"时间范围: {sales_report.reportSpecification.dataStartTime} - {sales_report.reportSpecification.dataEndTime}")
    print(f"市场ID: {sales_report.reportSpecification.marketplaceIds}")
    print(f"货币: {sales_report.reportSpecification.reportOptions.currencyCode}")
    print(f"总记录数: {len(sales_report.reportData)}")
    
    # 方法2：分析特定ASIN
    target_asin = "B0123456789"
    asin_analysis = analyze_asin_sales(sales_report, target_asin)
    
    print(f"\n--- ASIN {target_asin} 分析结果 ---")
    print(f"时间周期: {asin_analysis['time_period']}")
    print(f"市场: {asin_analysis['marketplace_ids']}")
    print(f"总销量: {asin_analysis['summary']['total_units']} 件")
    print(f"总收入: ${asin_analysis['summary']['total_revenue']}")
    print(f"平均价格: ${asin_analysis['summary']['average_price']}")
    print(f"记录数量: {asin_analysis['summary']['record_count']}")
    
    # 详细记录
    print(f"\n--- 详细销售记录 ---")
    for record in asin_analysis['detailed_records']:
        print(f"日期: {record.startTime[:10]}, 销量: {record.orderedUnits}, 收入: ${record.orderedRevenue}")


def example_inventory_report_processing():
    """示例：处理库存报告数据"""
    print("\n=== 库存报告处理示例 ===")
    
    # 模拟库存报告数据
    sample_inventory_data = {
        "reportSpecification": {
            "reportType": "GET_VENDOR_REAL_TIME_INVENTORY_REPORT",
            "dataStartTime": "2024-01-01T00:00:00Z",
            "dataEndTime": "2024-01-31T23:59:59Z",
            "marketplaceIds": ["ATVPDKIKX0DER"]
        },
        "reportData": [
            {
                "startTime": "2024-01-01T00:00:00Z",
                "endTime": "2024-01-01T23:59:59Z",
                "asin": "B0123456789",
                "highlyAvailableInventory": 1000
            },
            {
                "startTime": "2024-01-02T00:00:00Z",
                "endTime": "2024-01-02T23:59:59Z",
                "asin": "B0123456789",
                "highlyAvailableInventory": 950
            },
            {
                "startTime": "2024-01-03T00:00:00Z",
                "endTime": "2024-01-03T23:59:59Z",
                "asin": "B0123456789",
                "highlyAvailableInventory": 875
            }
        ]
    }
    
    # 解析库存报告
    inventory_report = create_inventory_report_from_json(json.dumps(sample_inventory_data))
    
    # 分析特定ASIN的库存
    target_asin = "B0123456789"
    inventory_analysis = analyze_asin_inventory(inventory_report, target_asin)
    
    print(f"--- ASIN {target_asin} 库存分析 ---")
    print(f"时间周期: {inventory_analysis['time_period']}")
    print(f"总库存: {inventory_analysis['summary']['total_inventory']}")
    print(f"平均库存: {inventory_analysis['summary']['average_inventory']}")
    print(f"记录数量: {inventory_analysis['summary']['record_count']}")
    
    print(f"\n--- 库存变化趋势 ---")
    for record in inventory_analysis['detailed_records']:
        print(f"日期: {record.startTime[:10]}, 库存: {record.highlyAvailableInventory}")


def example_report_download_and_parse():
    """示例：下载并解析报告文件"""
    print("\n=== 报告下载和解析示例 ===")
    
    # 这是一个模拟的工作流程，展示如何处理从Amazon API下载的报告
    def simulate_report_workflow(report_url: str, target_asin: str):
        """模拟完整的报告处理工作流程"""
        print(f"1. 下载报告文件: {report_url}")
        
        # 在实际使用中，这里会是真实的HTTP请求
        # response = requests.get(report_url)
        # 模拟下载的压缩文件内容
        
        print("2. 解压缩报告文件...")
        # 在实际使用中，这里会解压缩.gz文件
        # with gzip.open(BytesIO(response.content), 'rt') as f:
        #     json_content = f.read()
        
        # 模拟解压后的JSON内容
        json_content = json.dumps({
            "reportSpecification": {
                "reportType": "GET_VENDOR_REAL_TIME_SALES_REPORT",
                "dataStartTime": "2024-01-01T00:00:00Z",
                "dataEndTime": "2024-01-07T23:59:59Z",
                "marketplaceIds": ["ATVPDKIKX0DER"],
                "reportOptions": {"currencyCode": "USD"}
            },
            "reportData": [
                {
                    "startTime": "2024-01-01T00:00:00Z",
                    "endTime": "2024-01-01T23:59:59Z",
                    "asin": target_asin,
                    "orderedUnits": 25,
                    "orderedRevenue": 1250
                }
            ]
        })
        
        print("3. 解析JSON数据...")
        try:
            sales_report = create_sales_report_from_json(json_content)
            print("✓ 报告解析成功")
            
            print("4. 分析目标ASIN数据...")
            analysis = analyze_asin_sales(sales_report, target_asin)
            
            if analysis['summary']['record_count'] > 0:
                print(f"✓ 找到 {analysis['summary']['record_count']} 条 ASIN {target_asin} 的记录")
                print(f"  总销量: {analysis['summary']['total_units']}")
                print(f"  总收入: ${analysis['summary']['total_revenue']}")
                print(f"  平均价格: ${analysis['summary']['average_price']}")
            else:
                print(f"⚠ 未找到 ASIN {target_asin} 的数据")
                
        except ValueError as e:
            print(f"✗ 解析失败: {e}")
    
    # 模拟处理流程
    simulate_report_workflow("https://example.com/report.gz", "B0123456789")


def example_error_handling():
    """示例：错误处理"""
    print("\n=== 错误处理示例 ===")
    
    # 测试无效JSON
    try:
        invalid_json = "这不是有效的JSON"
        create_sales_report_from_json(invalid_json)
    except ValueError as e:
        print(f"✓ 正确捕获JSON解析错误: {e}")
    
    # 测试缺少字段的数据
    try:
        incomplete_data = {"reportSpecification": {}}
        create_sales_report_from_json(json.dumps(incomplete_data))
        print("✓ 成功处理不完整数据（使用默认值）")
    except Exception as e:
        print(f"处理不完整数据时出错: {e}")


if __name__ == "__main__":
    """运行所有示例"""
    print("Amazon数据结构使用示例")
    print("=" * 50)
    
    example_sales_report_processing()
    example_inventory_report_processing()
    example_report_download_and_parse()
    example_error_handling()
    
    print("\n" + "=" * 50)
    print("示例运行完成！")
    print("\n使用提示:")
    print("1. 在Python工具中导入: from src.tools.amazon_data_structures import *")
    print("2. 使用便捷函数: create_sales_report_from_json(), analyze_asin_sales()")
    print("3. 处理错误: 使用try-except捕获ValueError")
    print("4. 数据过滤: 使用AmazonReportParser.filter_by_asin()") 