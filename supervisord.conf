[supervisord]
nodaemon=true

[program:webserver]
command=uv run server.py --host 0.0.0.0 --port 8000
# 保证在/app目录下启动
# 依赖server.py在/app下
# autostart和autorestart保持true
directory=/app
autostart=true
autorestart=true
#日志输出到控制台
stdout_logfile=/dev/stdout
stderr_logfile=/dev/stderr
stdout_logfile_maxbytes=0
stderr_logfile_maxbytes=0

[program:larkbot]
command=uv run larkbot.py
# 保证在/app目录下启动
stdout_logfile=/app/logs/larkbot_stdout.log
stderr_logfile=/app/logs/larkbot_stderr.log
directory=/app
autostart=true
autorestart=true 